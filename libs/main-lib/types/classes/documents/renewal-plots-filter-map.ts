import { RenewalPlotsFilterEnum } from "../../enums";
import { TwoWayMap } from "../two-way-map.class";

export const RenewalPlotsFilterMap: TwoWayMap<
    RenewalPlotsFilterEnum,
    boolean | null
> = new TwoWayMap(
    [
        [RenewalPlotsFilterEnum.All, null],
        [RenewalPlotsFilterEnum.WithAutomaticRenewal, true],
        [RenewalPlotsFilterEnum.WithoutAutomaticRenewal, false],
    ],
    true
);
