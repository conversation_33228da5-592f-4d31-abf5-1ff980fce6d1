import { FieldContractTypeEnum } from "../../../enums";

export type ContractRenewalContractType = {
    c_num: string;
    due_date: string;
    can_renew: boolean;
    pc_rel_id: number;
    renew_area: number;
    start_date: string;
    contract_id: number;
    farming_name: string;
    contract_area: number;
    document_area: number;
    not_enought_area: number | null;
    new_contract_area: number;
    multi_year_contract: boolean;
    existing: ContractRenewalContractType | null;
    virtual_contract_type: string;
    contract_type: FieldContractTypeEnum;
    wrong_contract_period: boolean;
    is_from_sublease: boolean | null;
    is_sublease: boolean;
    is_annex: boolean;
    from_sublease: boolean;
};
