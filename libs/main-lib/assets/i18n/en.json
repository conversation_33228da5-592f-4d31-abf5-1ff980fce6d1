{"3 Leaves unfolded": "3 Leaves unfolded", "4 Leaves unfolded (Second Pair)": "4 Leaves unfolded (Second Pair)", "8 Leave sun folded": "8 Leaves unfolded", "A text filter has been applied, which will be applied to the remaining filters": "A text filter has been applied, which will be applied to the remaining filters.", "AB Humus": "AB Humus", "AB ISO control": "AB ISO control", "AB ISO full": "AB ISO full", "AB Leaf samples": "AB Leaf samples", "AB VRA control": "AB VRA control", "AB VRA full": "AB VRA full", "AB Overview": "AB Overview", "About Us": "About Us", "Absorption coefficient": "Absorption coefficient", "Absorption coefficient is required.": "Absorption coefficient is required.", "Accounts": "Accounts", "Actions": "Actions", "Active": "Active", "Archived": "Archived", "Active filters": "Active filters", "Active ingredient": "Active ingredient", "Active ingredients": "Active ingredients", "Active ingredients per unit of product": "Active ingredients per unit of product", "Activity by day": "Activity by day", "active substance": "active substance", "Add as a new": "Add as a new", "Add column": "Add column", "Add Comment": "Add Comment", "Add Crop": "Add Crop", "Add event": "Add event", "Add events": "Add events", "Add ingredient": "Add ingredient", "Add integration": "Add new integration", "Add layer to map": "Add layer to map", "Add map": "Add map", "Add marker": "Add marker", "Add platform": "Add new platform", "Add plots from map": "Please add plots from the map.", "Add plots to contract": "Add plots to contract", "Manage fields": "Manage fields", "Manage fields subscriptions": "Manage fields subscriptions", "Add plots": "Add plots", "Add prescription": "Add prescription", "Add product": "Add product", "Add profile": "Add profile", "Add event to EPORD": "Add event to EPORD", "Add task": "Add task", "Add the layer to compare on the left side of map": "Add the layer to compare on the left side of map", "Add the layer to compare on the right side of map": "Add the layer to compare on the right side of map", "Add weather station": "Add new weather station", "Add": "Add", "Added at": "Added at", "Add year": "Add year", "Add N, total, kg/dka a.s.": "Add N, total, kg/dka a.s.", "Add P2O5, kg/dka a.s.": "Add P2O5, kg/dka a.s.", "Add K2O, kg/dka a.s.": "Add K2O, kg/dka a.s.", "Add S, kg/dka a.s.": "Add S, kg/dka a.s.", "Add unit": "Add unit", "Address": "Address", "Admin": "Admin", "Administrative map": "Administrative map", "Aggressor": "Aggressor", "Agronomic tasks": "Agronomic tasks", "Agro spraying forecast": "Agro spraying forecast", "Air Temperature Max": "Air Temperature Max", "Air Temperature Min": "Air Temperature Min", "All categories": "All categories", "All plots must have the same crop.": "All plots must have the same crop.", "All existing crops for the selected plots and year will be overwritten": "All existing crops for the selected plots and year will be overwritten", "All products": "All products", "All selected features will be clipped to selected layer": "All selected features will be clipped to selected layer", "Allow prec": "Allow prec", "An error occurred while adding the product": "An error occurred while adding the product!", "An error occurred while adding the unit of measure": "An error occurred while adding the unit of measure!", "An error occurred while creating the task": "An error occurred while creating the task!", "An error occurred while declining the recommendation": "An error occurred while declining the recommendation", "An error occurred while deliting the product": "An error occurred while deliting the product!", "An error occurred while deliting the unit of measure": "An error occurred while deliting the unit of measure!", "An error occurred while loading potential event tasks": "An error occurred while loading potential event tasks!", "An error occured while recalculating covered area": "An error occured while recalculating covered area!", "An error occurred while updating the product": "An error occurred while updating the product!", "An error occurred while updating the task": "An error occurred while updating the task!", "An error occurred while updating the unit of measure": "An error occurred while updating the unit of measure!", "and": "and", "Analyses": "Analyses", "Angle": "<PERSON><PERSON>", "Annual fertilizer rate, soil application, kg/": "Annual fertilizer rate, soil application, kg/", "Analysis of soil samples": "Analysis of soil samples", "Analysis of soil samples for ph and macroelements": "Analysis of soil samples for ph and macroelements", "Analysis results": "Test results", "Analysis results report": "Analysis results report", "Applicant": "Applicant", "Apply": "Apply", "Apply filters": "Apply filters", "Approve results": "Approve results", "Approve": "Approve", "Approved": "Approved", "Application": "Application", "Application date": "Application date", "Application rate": "Application rate", "Area kvs": "Area kvs", "Applications and declarations art. 69 and 70": "Applications and declarations art. 69 and 70", "Are you sure delete this comment?": "Are you sure you want to delete this comment?", "Are you sure you want to delete this prescription": "Are you sure you want to delete this prescription?", "Are you sure you want to delete the event from this field": "Are you sure you want to delete the event from this field?", "Are you sure you want to delete this feature?": "Are you sure you want to delete this feature?", "Are you sure you want to delete these features?": "Are you sure you want to delete these features?", "Are you sure you want to delete this layer?": "Are you sure you want to delete this layer?", "Are you sure you want to delete this product": "Are you sure you want to delete this product?", "Are you sure you want to delete this unit of measure": "Are you sure you want to delete this unit of measure?", "Are you sure you want to delete this VRA map": "Are you sure you want to delete this VRA map", "Are you sure you want to delete the event": "Are you sure you want to delete the event?", "Are you sure you want to delete the events": "Are you sure you want to delete the events?", "Area by crops": "Area by crops", "Area DKA": "Area DKA", "Short.Area by crops": "Area by crops", "Area by date and type": "Area by date and type", "Area by package type": "Area by package type", "Area by recommendation status": "Area by recommendation status", "Area by status": "Area by status", "Area by VRA": "Area by VRA", "Area": "Area", "Area(ha)": "Area(ha)", "Area(dka)": "Area(dka)", "Area (dka)": "Area (dka)", "Area (approved)": "Area (approved)", "Area (proposed)": "Area (proposed)", "Area code": "Area code", "Area of intersection between KI and declared parcel (ha)": "Area of intersection between KI and declared parcel (ha)", "Atmospheric humidity": "Atmospheric humidity", "Attributes": "Attributes", "Available subscription": "Available subscription", "Average index analysis": "Average index analysis", "Average rate": "Average rate", "Avg index": "Avg. index", "AVG Water rate": "AVG. Water rate", "AVG NDVI": "AVG NDVI", "AVG Air Temperature": "AVG Clouds Percent", "Avg speed": "Avg speed", "Avg N rate": "Avg N rate", "LAST AVG. INDEX": "LAST AVG. INDEX", "Alternative crop": "Alternative crop", "Alternative crops": "Alternative crops", "Alternative crop is required.": "Alternative crop is required.", "Authenticate": "Authenticate", "Auto update": "Auto update after {{seconds}} sec", "Agronomist": "Agronomist", "Education": "Education", "Course": "Course", "Enter number of certificate": "Enter number of certificate", "Certificate number": "Certificate number", "Regional directorate of agriculture": "Regional directorate of agriculture", "Regional food safety directorate": "Regional food safety directorate", "Back to KVS list": "Back to KVS list", "Background color": "Background color", "Barcode": "Barcode", "Base unit": "Base unit", "Basic information": "Basic information", "Beneficiary": "Beneficiary", "Black Layer Stage": "Black Layer Stage", "Block": "Block", "Bluetooth connection is lost. GPS source is set to Internal.": "Bluetooth connection is lost. GPS source is set to Internal.", "boolean": "boolean", "Borders": "Borders", "Boundaries": "Boundaries", "Budding": "Budding", "Bulgarian": "Bulgarian", "By attribute": "By attribute", "By event type": "By event type", "By implement": "By implement", "By work operation": "By work operation", "bgn": "BGN", "BZS": "BZS", "BZZ number": "BZZ number", "Cancel": "Cancel", "Canceled": "Canceled", "Calculations": "Calculations", "Can't save map canvas": "Can't save map canvas!", "Close": "Close", "Cells": "Cells", "Cell ID": "Cell ID", "Cell area": "Cell area", "Chart data not available": "Chart data not available", "Clear filters": "Clear filters", "Clear": "Clear", "Clear all": "Clear all", "Clear selection": "Clear selection", "Clip": "Clip", "Clip fields": "Clip fields", "Collapse data panel": "Collapse data panel", "Collapse menu": "Collapse menu", "Coefficient": "Coefficient", "Color": "Color", "Color & size": "Color & size", "Column settings": "Column settings", "Column settings saved successfully": "Column settings saved successfully", "Column type": "Column type", "Columns": "Columns", "Successfully connected.": "Successfully connected.", "Combine layers geometry": "Combine layers geometry", "Connection error. Please check if external antenna is discoverable and turned on.": "Connection error. Please check if external antenna is discoverable and turned on.", "Company": "Company", "Company name": "Company name", "Company ID": "Company ID", "complete": "complete", "Completion date": "Completion date", "Confirm": "Confirm", "Confirm new password": "Confirm new password", "Confirm updating properties": "{{properiesCount}} properties will be updated.", "Contact name not found!": "Contact name not found!", "Contact person": "Contact person", "Contract ID": "Contract ID", "contract": "contract", "Contract": "Contract", "Contract area": "Contract area", "Contract date": "Contract date", "Contract period": "Contract period", "Contract not found": "Contract not found!", "Contract number": "Contract number", "Contract type": "Contract type", "Contracts": "Contracts", "Count.contracts": "contracts", "Count.documents": "documents", "Contracts for update": "Contracts for update", "Content": "Content", "Conclusion of contract from date": "Conclusion of contract from date", "Conclusion of contract To date": "Conclusion of contract To date", "Contract validity start date": "Contract validity start date", "Contract validity due date": "Contract validity due date", "Contract duration in years": "Contract duration in years", "Contractor": "Contractor", "Contractor EIK/PID": "Contractor EIK/PID", "Contractor name": "Contractor name", "Contract start date": "Contract start date", "Contract end date": "Contract end date", "Comment": "Comment", "Comments": "Comments", "Compound": "Compound", "Consumed fuel idle": "Consumed fuel idle", "Consumed fuel in motion": "Consumed fuel in motion", "Consumed fuel total": "Consumed fuel total", "Consumed fuel l/ha": "Consumed fuel l/ha", "Consumed fuel l/100km": "Consumed fuel l/100km", "Consumption rate": "Consumption rate", "Count": "Count", "Covered area": "Covered area", "Covered area cannot be greater than the area of the field": "Cultivated area cannot be greater than the area of the field", "Cut off": "Cut off", "Cut out": "Cut out", "Plot area": "Plot area", "Plot status": "Plot status", "plot status": "slot status", "Cost": "Cost", "Copied to clipboard": "Copied to clipboard", "Copy": "Copy", "Copy layer": "Copy layer", "Copy to clipboard": "Copy to clipboard", "Create": "Create", "Create a similar prescription": "Create a similar prescription", "Create from table": "Create from table", "Create grids": "Create grids", "Create recommendation": "Create recommendation", "Create new map": "Create new map", "Crop development": "Crop development", "Crop": "Crop", "Crops": "Crops", "Crop history": "Crop history", "Crop rotation": "Crop rotation", "Cross-sectional area": "Cross-sectional area", "Cross-sectional area(dka)": "Cross-sectional area(dka)", "Cross-sectional layer": "Cross-sectional layer", "Cultivated area": "Cultivated area", "Current": "Current", "Current crop": "Current crop", "Current phenological phase": "Current phenological phase", "Custom name": "Custom name", "custom_name": "Custom name", "Choose a layer and click the features on the map to select them": "Add profile a layer and click the features on the map to select them", "Choose your own or leave empty to auto-generate": "Choose your own or leave empty to auto-generate", "Consumer": "Consumer", "Car": "Car", "DKA": "dka", "Pre blister-Milk maturity": "Pre blister-Milk maturity", "Data not available": "Data not available", "Data from FarmTrack": "Data from FarmTrack", "Data from FarmTrack, click for info": "Data from FarmTrack, click for info", "Datasets": "Datasets", "date": "date", "Date": "Date", "Date of imaging": "Date of imaging", "Date of imaging is required.": "Date of imaging is required.", "Date range": "Date range", "Day": "Day", "Days": "Days", "Days since sowing": "Days since sowing", "Deactivate": "Deactivate", "Declaration type": "Declaration type", "Declared area(dka)": "Declared area (dka)", "Declared area status": "Declared area status", "Declared by": "Declared by", "Development stage": "Development stage", "Decline": "Decline", "Default application rate": "Default application rate", "Default price": "Default price", "Delete": "Delete", "Delete all": "Delete all", "Delete from all diaries": "Delete from all diaries", "Delete layer confirmation": "Are you sure you want to delete layer {{layerName}}?", "Delete unit?": "Delete unit {{unit}}?", "Deleting...": "Deleting...", "Delivery frequency": "Delivery frequency", "Delivery time": "Delivery time", "This will delete the unit and all events rеlated to it. Are you sure?": "This will delete the unit '{{ unit }}' and all events ralated to it. Are you sure?", "Delete marker": "Delete marker", "Delete scheduled report": "Delete scheduled report?", "Denominator": "Denominator", "Density": "Density", "Description": "Description", "Details": "Details", "Depth, cm": "Depth, cm", "Demo sampling": "Demo sampling", "Distance": "Distance", "Disease": "Disease", "Difference": "Difference", "Difference (Substance)": "Difference (Substance)", "Difference (Product)": "Difference (Product)", "Discard changes": "Discard changes", "Document": "Document", "documents": "documents", "document": "document", "Document area": "Document area", "Ownership document area": "Ownership document area", "Document area(dka)": "Document area(dka)", "Documents": "Documents", "Done": "Done", "Done (approved)": "Done (approved)", "Done (proposed)": "Done (proposed)", "Dose": "<PERSON><PERSON>", "Dough Maturity": "Dough Maturity", "Download Excel report": "Download Excel report", "Download PDF report": "Download PDF report", "Download Protocol": "Download Protocol", "Drag points on map to edit them": "Drag points on map to edit them", "Draw a line over the layer to split it": "Draw a line over the layer to split it", "Draw a polygon over the holes you want to fill": "Draw a polygon over the holes you want to fill", "Draw a shape over the layer to create a hole": "Draw a shape over the layer to create a hole", "Draw feature": "Draw feature", "Driver": "Driver", "Due date": "Due date", "Double click to finish drawing.": "Double click to finish drawing.", "Download": "Download", "Download PDF file": "Download PDF file", "Duration": "Duration", "Duration (hours)": "Duration (hours)", "Duration (in years)": "Duration (in years)", "Distance (approved)": "Distance (approved)", "Distance (proposed)": "Distance (proposed)", "Ear": "Ear", "EIK/PID": "EIK/PID", "Edit covered area": "Edit covered area", "Edit feature": "Edit feature", "Edit field": "Edit field", "Edit fields": "Edit fields", "Edit filter": "Edit filter", "Edit geometry": "Edit geometry", "Edit implement width": "Edit implement width", "Edit in all diaries": "Edit in all diaries", "Edit integration": "Edit integration", "Edit marker": "Edit marker", "Edit platform": "Edit platform", "Edit prescription": "Edit prescription", "Edit prescription warning": "You are editing a prescription included in {{diariesCount}} diaries. To edit it everywhere, check 'Edit in all diaries' at the end of the form.", "Edit product": "Edit product", "Edit scheduled report": "Edit scheduled report", "Edit selected field": "Edit selected field", "Edit selected fields": "Edit selected fields", "Edit task": "Edit task", "Edit weather station": "Edit weather station", "Edit": "Edit", "Edit multiple": "Edit multiple", "Edit unit": "Edit unit", "Editable boundaries": "Editable boundaries", "ekatte": "ekatte", "EKATTE": "EKATTE", "Email": "E-mail", "Email address": "E-mail address", "Email addresses": "E-mail addresses", "E-mail is required": "E-mail is required", "Emergence Stage": "Emergence Stage", "Element": "Element", "End": "End", "End time": "End time", "End date": "End date", "English": "English", "Enter": "Enter", "Enter application": "Enter application", "Enter coefficient": "Enter coefficient", "Enter covered area": "Enter covered area", "Enter default price": "Enter default price", "Enter email error": "Please enter e-mail address", "Enter full name": "Enter full name", "Enter harvest date error": "Please enter harvest date", "Enter implement width": "Enter implement width", "Enter information": "Enter information", "Enter latitude error": "Please enter latitude", "Enter layer name": "Enter layer name", "Enter longitude error": "Please enter longitude", "Enter name": "Enter name", "Enter name error": "Please enter a name", "Enter pest name": "Enter pest name", "Enter predecessor": "Enter predecessor", "Enter quantity": "Enter quantity", "Enter quarantine period": "Enter quarantine period", "Enter rate": "Enter rate", "Enter short name": "Enter short name", "Enter sowing date error": "Please enter sowing date", "Enter subject error": "Please enter subject", "Enter Unit ID": "Enter Unit ID", "Enter Unit IMEI": "Enter Unit IMEI", "Enter year": "Please enter a year", "Enter value": "Enter value", "Enter variety": "Enter variety", "Enter municipality": "Enter municipality", "Enter locality": "Enter locality", "Enter address": "Enter address", "Enter identity number": "Enter identity number", "Enter agronomist name": "Enter agronomist name", "Enter warehouse for plant production": "Enter warehouse for plant production", "Entity editing": "Entity editing", "Event type": "Event type", "Event stage": "Event stage", "Events": "Events", "Events report": "Events report", "Events to the plot": "Events to the plot", "Exact matching number": "Exact matching number", "Execute": "Execute", "Existing boundaries": "Existing boundaries", "Exit": "Exit", "Expand data panel": "Expand data panel", "Export": "Export", "Export report error": "The report cannot be exported at this time", "Export to Excel": "Export to Excel", "Export(XLS)": "Export(XLS)", "Export with cadastral map format": "Export with cadastral map format", "Expected yield": "Expected yield", "Expected yield, kg/": "Target yield, kg/", "Expected yield is required.": "Expected yield is required.", "Expected yield must be greater than 0.": "Expected yield must be greater than 0.", "Error": "Error", "Error adding integration": "Error adding integration!", "Error copying fields to layer": "Error copying fields to layer", "Error exporting VRA maps": "Error exporting VRA maps!", "Error loading attribute edit details": "Error loading attribute edit details", "Error loading attributes": "Error loading attributes", "Error loading implements list": "Error loading implements list!", "Error loading layer personalization": "Error loading layer personalization", "Error loading tasks timeline": "Error loading tasks timeline", "Error loading field details": "Error loading field details", "Error loading dropdown source": "Error loading dropdown source", "Error loading filter options": "An error occured while loading the options for filter {{filterName}}", "Error saving attribute info": "Error saving attribute info", "Error saving feature details": "Error saving feature details!", "Error showing device heading": "Error showing device heading!", "Error updating integration": "Error updating integration!", "Estate": "Estate", "Expired": "Expired", "Expired or cancelled": "Expired or cancelled", "Humus content must be greater than 0.": "Humus content must be greater than 0.", "FT integrations": "FT integrations", "Failed to export product": "Failed to export product", "Failed to load contracts for the selected field": "Failed to load contracts for the selected field", "Failed to load requested cadastral property report": "Failed to load requested cadastral property report", "Failed to load farm years": "Failed to load farm years", "Failed to load features from map": "Failed to load features from map", "Failed to load product": "Failed to load product", "Failed to load products": "Failed to load products", "Failed to load properties": "Failed to load KVS properties", "Failed to load selected legend": "Failed to load selected legend", "Failed to save column settings": "Failed to save column settings", "Failed to load documents for the selected field": "Failed to load documents for the selected field", "Farm": "Farm", "Farm data": "Farm data", "Farm track": "Farm Track", "Farm year start": "Farm year start", "Farm year end": "Farm year end", "Farming year": "Farming year", "Farming years": "Farming years", "FarmTrack Integrations": "FarmTrack Integrations", "Farms": "Farms", "Feature details saved successfully": "Feature details saved successfully!", "Fertiliser": "Fertiliser", "Field": "Field", "Field capacity": "Field capacity", "City": "City", "Field name": "Field name", "Field saved successfully": "Field saved successfully!", "Fields": "Fields", "Fields synced successfully": "<PERSON> synced successfully!", "File type": "File type", "Fill": "Fill", "Filling": "Filling", "Filling a hole must be within the boundaries of the selected field!": "Filling a hole must be within the boundaries of the selected field!", "Filter": "Filter", "Filters": "Filters", "Filter group": "Filter group", "Filter fields by work operation": "Filter fields by work operation", "Filter is available for Land properties subscriptions. Please contact your sales representative to get more info.": "Filter is available for Land properties subscriptions. Please contact your sales representative to get more info.", "Finalize": "Finalize", "Find": "Find", "Find on map": "Find on map", "Finish": "Finish", "First branch": "First branch", "First branch-Budding": "First branch-Budding", "First side shoot detectable": "First side shoot detectable", "Flat rate": "Flat rate", "Flat rate is required.": "Flat rate is required.", "Flat rate (kg/ha active substance)": "Flat rate (kg/ha active substance)", "Flat rate (Substance)": "Flat rate (Substance)", "Flat rate (Product)": "Flat rate (Product)", "Flat rate of fertilization": "Flat rate of fertilization", "Flowering (Anthesis)": "Flowering (Anthe<PERSON>)", "Flowering": "Flowering", "Flowering-Silking": "Flowering-Silking", "Flowering-Maturing": "Flowering-Maturing", "For recommendation": "For recommendation", "For sampling": "For sampling", "Rosette (4-6 leaves)": "<PERSON><PERSON> (4-6 leaves)", "Rosette (6 leaves)": "Rosette (6 leaves)", "Rosette (6-8 leaves)": "<PERSON><PERSON> (6-8 leaves)", "Rosette (8-10 leaves)": "<PERSON><PERSON> (8-10 leaves)", "Follow us on": "Follow us on", "Font size": "Font size", "For edit": "For edit", "for_sublease": "for sublease", "Forecast": "Forecast", "Formation of inflorescence": "Formation of inflorescence", "Formation of inflorescences-Flowering": "Emergence of inflorescences-Flowering", "Fourth pair leaf": "Fourth pair leaves", "from": "from", "Fuel": "Fuel", "Fuel consumed idling": "Fuel consumed idling", "Fuel consumed in motion": "Fuel consumed in motion", "Fuel consumption": "Fuel consumption", "Fuel (approved)": "Fuel (approved)", "Fuel (proposed)": "Fuel (proposed)", "Full maturity": "Full maturity", "Full name": "Full name", "Fully ripe": "Fully ripened", "Foliar application (* elements that are recommended to be applied in higher concentration)": "Foliar application (* elements that are recommended to be applied in higher concentration)", "Failure": "Failure", "GDD": "GDD", "General information": "General information", "Geoindex": "Geoindex", "geom": "geometry", "Geometric": "Geometric", "geometry": "geometry", "Geometry": "Geometry", "Geometry area": "Geometry area", "Geometry area(dka)": "Geometry area(dka)", "Geometry saved successfully": "Geometry saved successfully", "Germination": "Germination", "Get the app": "Get the app", "Get your management app on Android and IOS": "Get your management app on Android and IOS", "GPS Source": "GPS Source", "GPS signal acquired": "GPS signal acquired", "Go to the boundary of the measured area and press Start.": "Go to the boundary of the measured area and press Start.", "Grid Type": "Grid Type", "Group by": "Group by", "Group by fill column": "Group by fill column", "Grouping": "Grouping", "Grow/Тhird sheet": "Emergence-Тhird leaf", "Growing conditions": "Growing conditions", "Growing stage": "Growth stage", "Harvest date": "Harvest date", "Has been treated with the product": "Has been treated with the product", "Hide": "<PERSON>de", "Hide advanced": "<PERSON><PERSON> advanced", "Hide all selected layers": "Hide all selected layers", "Hide all system layers": "Hide all system layers", "Hide base map": "Hide base map", "Hide border": "Hide border", "Hide covered area edit": "Hide covered area edit", "Hide filling": "Hide filling", "Hide filters": "Hide filters", "Hide group": "Hide group", "Hide implement width edit": "Hide implement width edit", "Hide layer": "Hide layer", "Hide legend": "Hide legend", "Hide search": "Hide search", "Hide tasks": "Hide tasks", "Hide track": "Hide track", "High risk": "High risk", "High vegetation": "More vegetation", "Highlight filtered items on map": "Highlight filtered items on map", "History": "History", "Home": "Home", "Hour": "Hour", "Hours": "Hours", "Humus content": "Humus content", "Humus is required.": "<PERSON><PERSON> is required.", "Harvester": "Harvester", "ID": "ID", "Identifier": "Identifier", "Index adjustment": "Index adjustment", "Index history": "Index history", "Indexes": "Indexes", "Inheritor": "Inheritor", "Inheritor ID": "Inheritor ID", "Inheritor name": "Inheritor name", "Ingredient": "Ingredient", "into": "to", "IM integrations": "IM integrations", "Image": "Image", "Images": "Images", "Implements": "Implements", "implements": "implements", "implements_count": "implements", "Implement": "Implement", "implement": "implement", "Implement ID": "Implement ID", "Implement width": "Implement width", "Implement width must be between 0 and 50 meters": "Implement width must be between 0 and 50 meters", "Implement name or width cannot be empty": "Implement name or width cannot be empty!", "Import layer": "Import layer", "Information": "Information", "Internal Server Error. Our team is notified and working to resolve the problem.": "Internal Server Error. Our team is notified and working to resolve the problem.", "Internal sublease to": "Internal sublease to", "No trailer": "No trailer", "No recommendation": "No recommendation", "Inactive": "Inactive", "Inactive from": "Inactive from", "Inflorescence Emergence (Ear Emergence)": "Inflorescence Emergence (Tasseling)", "Inflorescence Emergence (Green Bud)": "Inflorescence Emergence (Green Bud)", "Inflorescence Emergence (Heading)": "Inflorescence Emergence (Heading)", "Inflorescence Emergence": "Inflorescence Emergence", "Initiate a device location trace to begin.": "Initiate a device location trace to begin.", "Install date": "Install date", "install_date": "Install date", "Insert manualy the type of product": "Insert manualy the type of product", "integration_address": "server", "Integrations history": "Integrations history", "Integrations": "Integrations", "Integration summary": "Integration summary", "Intersect": "Intersect", "Intersection description": "Intersection description", "intersection_desc_cut_out": "<p>Cut out tool is most often used for cutting the boundaries of the fields based on the layer with areas eligible for subsidies or cadastral plots. Cut out tool will leave only parts from layer A which are entirely contained in the geometries of layer B. At least 1 feature shall be selected from both layers - A and B.</p><p>Feature from layer A is the currently edited feature.</p><p>Feature/s from layer B (select layer from the dropdown and click on map to select features)</p><p>Result of the intersection.</p>", "intersection_desc_cut_off": "<p>Cut off tool is used to split the areas which are outside a specific layer into new features. Most often these are layer with areas eligible for subsidies or NATURA 2000 areas. Cut off tool will leave only parts from layer A which are outside the geometry/es of layer B. At least 1 feature shall be selected from both layers - A and B.</p><p></p><p>Feature from layer A is the currently edited feature.</p><p>Feature/s from layer B (select layer from the dropdown and click on map to select features)</p><p>Result of the intersection.</p>", "intersection_desc_split": "<p>Split tool is useful when one feature needs to be split according to layers with areas eligible for subsidies or cadastral plots and both (contained and outside) parts are needed for further work. The split tool will produce separate geometries from the feature in layer A  according to the boundaries of layer B. At least 1 feature shall be selected from both layers - A and B.</p><p>Feature from layer A is the currently edited feature.</p><p>Feature/s from layer B (select layer from the dropdown and click on map to select features)</p><p>Result of the intersection.</p>", "Invalid boundaries asd": "Invalid boundaries", "Invalid email": "The e-mail you've entered is not valid!", "Invalid emails": "The field contains invald e-mails!", "Invalid title": "Invalid title", "Invalid username": "Invalid username", "Invalid username or password": "Invalid credentials", "Creditor": "Creditor", "Irrigated": "Irrigated", "Irrigated area": "Irrigated area", "Irrigation": "Irrigation", "Irrigation management": "Irrigation management", "Irrigation mgmt": "Irrigation mgmt", "Irrigation monitoring": "Irrigation monitoring", "Irrigation tasks": "Irrigation tasks", "Irrigation tasks report": "Irrigation tasks report", "irrigation_tasks_report": "Irrigation tasks report", "ISAK number": "ISAK number", "Lab results": "Lab results", "Lab data": "Lab data", "Lab ID": "Lab ID", "Lab number": "Lab number", "Label": "Label", "Labels": "Labels", "Label size": "Label size", "Lat": "Lat", "Last communication": "Last communication", "Last message": "Last message", "Last update": "Last update", "Layer": "Layer", "Layers": "Layers", "Layer contains all columns from the source layer": "Layer contains all columns from the source layer.", "Layer name": "Layer name", "Layers intersection": "Layers intersection", "Layer personalization": "Layer personalization", "Layer does not contain all columns from source layer": "Layer does not contain all columns from source layer.", "Less vegetation": "Less vegetation", "Lessor": "<PERSON><PERSON>", "KAD ident": "KAD ident", "Kernel Blister Stage": "Kernel Blister Stage", "Kernel Dough Stage": "Kernel Dough Stage", "Kernel Milk Stage": "Kernel Milk Stage", "Land": "Land", "Landscape": "Landscape", "large": "large", "Leaf formation": "Vegetative growth", "Leaf formation-Silking": "Vegetative growth-Silking", "Legal rights": "Legal rights", "Legend": "Legend", "Length": "Length", "Left": "Left", "Link to task": "Link to task", "Linked accounts message": "All available accounts are linked.", "Linked stations": "Linked weather stations", "Load more": "Load more", "Log out": "Log out", "Login to GeoScan": "Login to GeoScan", "Login to EPORD": "Login to EPORD", "Login": "<PERSON><PERSON>", "Login failed": "Login failed!", "Loading Data": "Loading Data", "Locality": "Locality", "Location settings": "Location settings", "Lon": "<PERSON>", "Low risk": "Low risk", "LUD": "LUD", "Main": "Main", "Machine": "Machine", "Machine name": "Machine name", "Machines": "Machines", "Machines events report": "Machines events report", "Manage integrations": "Manage integrations", "Manage stations": "Manage stations", "Manage weather stations": "Manage weather stations", "Manage sampling": "Manage sampling", "Map": "Map", "Map layers": "Map layers", "Map Old": "Map Old", "Map name": "Map name", "Map selection": "Map selection", "Map title": "Map title", "Maps": "Maps", "Margin": "<PERSON><PERSON>", "Markers": "Markers", "Maturing": "Maturing", "Max speed": "Max speed", "Medium risk": "Medium risk", "Merge": "<PERSON><PERSON>", "Message": "Message", "MIN Air Temperature": "MIN Air Temperature", "MAX Air Temperature": "MAX Air Temperature", "Milk maturity": "Milk maturity", "Milk maturity-Wax maturity": "Milk maturity-Wax maturity", "millilitre": "millilitre", "milligram": "milligram", "Macroelementrs": "Macroelementrs", "Microelements": "Microelements", "Moisture": "Moisture", "Moisture index": "Moisture index", "Move to top": "Move to top", "Movement duration": "Movement duration", "More": "More", "more": "more", "More actions": "More actions", "more selected": "more selected", "Multi edit crops": "Multi edit crops", "Multi-edit": "Multi-edit", "Municipality": "Municipality", "Мunicipality name": "Мunicipality name", "N/A": "N/A", "Name": "Name", "name": "Name", "Name is required": "Name is required", "Name of municipality": "Name of municipality", "NDVI nitrogen": "NDVI nitrogen", "Near": "Near", "New": "New", "New boundaries": "New boundaries", "New marker": "New marker", "New password": "New password", "New prescription for fertiliser": "New prescription for fertiliser", "New prescription for PPP": "New prescription for PPP", "New task": "New task", "New feature": "New feature", "Next": "Next", "No": "No", "No active layer selected": "No active layer selected", "No Crop": "No Crop", "No change": "No change", "No driver": "No driver", "Not editable boundaries": "Not editable boundaries", "No field": "No field", "No fill": "No fill", "No guidance lines": "No guidance lines", "No implement": "No implement", "No implements with selected combination of work operations available": "No implements with selected combination of work operations available", "No machine": "No machine", "No matching tasks found": "No matching tasks found", "No name": "No name", "no_participate": "no participate", "No plot info": "There is no information for the selected plot", "No products": "No products", "No surveys found": "No surveys found", "No value": "No value", "No VRA maps": "No VRA maps", "No VRA maps to show": "No VRA maps to show", "No work operations": "No work operations", "North arrow": "North arrow", "Nothing here": "There's nothing here", "Notifications": "Notifications", "Note": "Note", "ntp": "ntp", "NTP": "NTP", "Number of classes": "Number of classes", "Number of classes is required.": "Number of classes is required.", "Number of contracts": "Number of contracts", "Number of documents": "Number of documents", "Number of plots": "Number of plots", "Number of properties": "Number of properties", "Numerator": "Numerator", "Nutrition": "Fertilization", "Nutrition recommendations (balanced fertilization plan)": "Recommendations for crop fertilization (Crop Fertilization Plan)", "Objects union": "Objects union", "NUZ": "NUZ", "Ok": "Ok", "on": "on", "One property will be updated": "One property will be updated.", "Ongoing": "Ongoing", "Only letters, numbers, spaces, braces, hyphens (-), and forward slashes (/) are allowed": "Only letters, numbers, spaces, braces, hyphens (-), and forward slashes (/) are allowed", "OR": "OR", "Organization": "Organization", "Organizations": "Organizations", "Organization manager": "Organization manager", "organization_id": "Organization ID", "Organization does not own ekatte": "Organization does not own ekatte", "Order to declare the zone": "Order to declare the zone", "Orientation": "Orientation", "Outline hole": "Outline hole", "overdeclared": "overdeclared", "Overlapping boundaries": "Overlapping boundaries", "Overlaps": "Overlaps", "Overlaps less than 20%": "Overlaps less than 20%", "Owners osz": "Owners osz", "Owners OSZ": "Owners OSZ", "Owner": "Owner", "Owner, Representative, Inheritors": "Owner, Representative, Inheritors", "Owner, Representative, Inheritors, ID": "Owner, Representative, Inheritors, ID", "Owner, Representative, Inheritors, name": "Owner, Representative, Inheritors, name", "Owner name": "Owner name", "Owner ID": "Owner ID", "Owner from OSZ data": "Owner from OSZ data", "Owner From OSZ ID": "Owner From OSZ ID", "Owner From OSZ name": "Owner From OSZ name", "Owner's address": "Owner's address", "Owner's phone": "Owner's phone", "Ownership": "Ownership", "Ownership document date": "Ownership document date", "Ownership document number": "Ownership document number", "Owner EIK/PID": "Owner EIK/PID", "Package": "Package", "Packages": "Packages", "package_period": "Period", "Page size": "Page size", "Pan": "Pan", "participate": "participate", "Participation art.37v": "Participation art.37v", "Password": "Password", "Password is required": "Password is required!", "Password must contain at least one uppercase letter, one number and no spaces! Only Latin letters are allowed": "Password must contain at least one uppercase letter, one number and no spaces! Only Latin letters are allowed!", "Password should be min 6 chars long": "Password should be min 6 chars long!", "Password successfully updated": "Password successfully updated!", "Passwords do not match": "Passwords do not match!", "Pause": "Pause", "PDF": "PDF", "Pest": "Pest", "per": "per", "per dka": "per dka", "per ha": "per ha", "per km": "per km", "per 100km": "per 100km", "Percent": "Percent", "Period": "Period", "Period between applications": "Period between applications", "Phone": "Phone", "Phone number not found!": "Phone number not found!", "Phone number must contain only digits": "Phone number must contain only digits!", "Phenophase": "Phenophase", "Physical block": "Physical block", "Physical block area(ha)": "Physical block area(ha)", "Physical block number": "Physical block number", "Pivot current position": "Pivot current position", "Planned": "Planned", "Planned crop": "Planned crop", "Planned crops": "Planned crops", "Planned crop is required": "Planned crop is required.", "Platform ID": "Platform ID", "Platform name": "Platform name", "Platform": "Platform", "Platforms": "Platforms", "Please confirm your password": "Please confirm your password!", "Please enter a valid e-mail address": "Please enter a valid e-mail address", "Please enter your password": "Please enter your password", "Please enter your username": "Please enter your username", "Please input alternative crop name or delete this field.": "Please input alternative crop name or delete this field.", "Please select your fertilization type!": "Please select your fertilization type!", "Please select your date of imaging!": "Please select your date of imaging!", "Please select your number of classes!": "Please select your number of classes!", "Please select your product!": "Please select your product!", "Please select one or more rows first": "Please select one or more rows first!", "Plot": "Plot", "Plot ID": "Plot ID", "Plot is outside weather station coverage": "Plot is outside weather station coverage", "Plot name": "Plot name", "Pivot": "Pivot", "Plots that can be added": "Plots that can be added", "Plots that can't be added": "Plots that can't be added", "Plots": "Plots", "Plots by crop": "Plots by crop", "Plot data": "Plot data", "plot in": "plot in", "Plot information": "Plot information", "plots in": "plots in", "point_above_average": "{{value}} point above average", "point_below_average": "{{value}} point below average", "Popular": "Popular", "Portrait": "Portrait", "Potential event tasks": "Potential event tasks", "Precipitation": "Precipitation", "Precipitation probability": "Precipitation probability", "Precipitation Sum": "Precipitation Sum", "Precipitation Sum Above": "Precipitation Sum Above 5mm", "Prescription with unique combination of fields already exists.": "Prescription with unique combination of fields already exists.", "Prescriptions": "Prescriptions", "Pressure": "Pressure", "Press back to return to KVS list": "Press back to return to KVS list.", "Press the button to redraw the feature.": "Press the button to redraw the feature.", "Previous": "Previous", "Preview": "Preview", "Preview Recommendation": "Preview Recommendation", "Primary crop": "Main crop", "Print": "Print", "Problem with generating the export files": "Problem with generating the export files", "Process": "Process", "Product": "Product", "Product ID": "Product ID", "Product is required.": "Product is required.", "Product name": "Product name", "Product price": "Product price", "Product price is required.": "Product price is required.", "Product price per": "Product price per", "Product type": "Product type", "Products": "Products", "Product with unique combination of fields trade name, dose, phenophase and applicable crop already exists.": "Product with unique combination of fields trade name, dose, phenophase and applicable crop already exists.", "Product with unique combination of fields trade name, dose and applicable crop already exists.": "Product with unique combination of fields trade name, dose and applicable crop already exists.", "Properties with active contracts for": "Properties with active contracts for", "Properties with expiring contracts for": "Properties with expiring contracts for", "Properties successfully updated": "Properties successfully updated", "Property area": "Property area", "Property area(dka)": "Property area(dka)", "Property area PZP(dka)": "Property area PZP(dka)", "Proposed": "Proposed", "Provide your login information below": "Provide your login information below", "px": "px", "Qty": "Qty", "Quantity": "Quantity", "Quarantine period": "Quarantine period", "Radius": "<PERSON><PERSON>", "radius": "<PERSON><PERSON>", "Rainfall": "Rainfall", "Rainfall since sowing": "Rainfall since sowing", "Rate": "Rate", "Real color": "Real color", "Real time monitoring of your crops on your mobile device.": "Real time monitoring of your crops on your mobile device.", "Recalculate": "Recalculate", "Reccuring": "Reccuring", "Records": "Records", "Recommendation": "Recommendation", "Recommendations": "Recommendations", "Recommendation data": "Recommendation data", "Recommendations report": "Recommendations report", "Refresh": "Refresh", "Reject": "Reject", "Remote ID": "Remote ID", "Remote username": "Remote username", "Remove": "Remove", "Remove driver": "Remove driver", "Remove field": "Remove field", "Remove filter": "Remove filter", "Remove guidance line": "Remove guidance line", "Remove hole": "Remove hole", "Remove implement": "Remove implement", "Remove machine": "Remove machine", "Remove product": "Remove product", "Remove VRA map": "Remove VRA map", "Remove work operation": "Remove work operation", "Report category": "Report category", "Replace": "Replace", "Report name": "Report name", "Report not sent": "Report was not sent!", "Report sent": "Report successfully sent!", "Report scheduled successfully": "Report scheduled successfully!", "Report not scheduled successfully": "Report not scheduled successfully!", "Reports": "Reports", "Reset map": "Reset map", "Results for approve": "Results for approval", "Results": "Results", "Results of the analysis of soil samples for ph and macroelements": "Soil test results for pH and macroelements", "Representative": "Representative", "Representative ID": "Representative ID", "Representative name": "Representative name", "Resume": "Resume", "Retry": "Retry", "Ripening Stage": "Ripening Stage", "Ripening": "Ripening", "Right": "Right", "Rosette Stage (10 Leaves unfolded)": "Rosette Stage (10 Leaves unfolded)", "Rosette Stage (4 Leaves unfolded)": "Rosette Stage (4 Leaves unfolded)", "Rosette Stage (6 Leaves unfolded)": "Rosette Stage (6 Leaves unfolded)", "Rosette Stage (8 Leaves unfolded)": "Rosette Stage (8 Leaves unfolded)", "Sampler": "<PERSON><PERSON>", "Satellite imagery": "Satellite imagery", "Satellite": "Satellite", "Save as": "Save as", "save as JPEG": "save as JPEG", "save as PDF": "save as PDF", "save as PNG": "save as PNG", "Save changes": "Save changes", "Save feature": "Save feature", "Save field": "Save field", "Save legend": "Save legend", "Save sampling changes": "Save sampling changes", "Satellite analysis": "Satellite", "Save": "Save", "Scale": "Scale", "Schedule": "Schedule", "Schedule report": "Schedule report", "Scheduled": "Scheduled", "Scheduled reports": "Scheduled reports", "Schema": "<PERSON><PERSON><PERSON>", "Scheme": "Scheme", "screen size": "screen size", "Search barcode": "Search barcode", "Search lab number": "Search lab number", "Search Organization": "Search organization", "Search by name": "Search by name", "Search by name or crop": "Search by crop", "Search in results": "Search in results", "Search tip": "Type at least 3 letters to search", "Show all selected layers": "Show all selected layers", "Show all system layers": "Show all system layers", "Show base map": "Show base map", "Show border": "Show border", "Show by": "Show by", "Show filling": "Show filling", "Show group": "Show group", "Show layer": "Show layer", "Type at least 2 letters to search": "Type at least 2 letters to search", "Search plot": "Search plot", "Search Crop": "Search crop", "Search implement": "Search implement", "Search machine": "Search machine", "Search platform": "Search platform", "Search": "Search", "Searching for GPS signal …": "Searching for GPS signal …", "Second pair leaf": "Second pair of leaves", "Secondary crop": "Secondary crop", "Secondary crops": "Secondary crops", "Select": "Select", "Select Absorption model": "Select Absorption model", "Select active layer to display data": "Select active layer to display data", "Select a later change date from the drop-down menu or create an addendum in the contract with a corresponding end date to the one selected": "Select a later change date from the drop-down menu or create an addendum in the contract with a corresponding end date to the one selected.", "Select a subscription": "Select a subscription", "Select all": "Select all", "Select base unit": "Select base unit", "Select category": "Select category", "Select columns": "Select columns", "Select contract error": "Please select a contract", "Select contract": "Select contract", "Select crop": "Select crop", "Select crops": "Select crops", "Select dates": "Select dates", "Select date": "Select date", "Select date range from calendar": "Select date range from calendar to display data", "Select denominator": "Select denominator", "Select drawing option": "Select drawing option", "Select driver": "Select driver", "Select element": "Select element", "Select date of imaging": "Select date of imaging", "Select event type": "Select event type", "Select farm error": "Please, select a farm", "Select farm": "Select a farm", "Select farm year": "Select farm year", "Select two or more features from the same layer": "Select two or more features from the same layer", "Select task states": "Select task states", "Select fertilization type": "Select fertilization type", "Select field": "Select а field", "Select file type": "Select file type", "Select file type error": "Please, select file type", "Select filters or select from map": "Select filters or select from map", "Select filter": "Select filter", "Select for sampling": "Select for sampling", "Select from map": "Select from map", "Select guidance lines": "Select guidance lines", "Select implement": "Select implement", "Select intersection type": "Select intersection type", "Select layer": "Select layer", "Select a layer to continue": "Select a layer to continue", "Select map layers": "Select map layers", "Select name": "Select name", "Select numerator": "Select numerator", "Select packages": "Select packages", "Select period error": "Please select a period", "Select period": "Select a period", "Select а product": "Select а product", "Select platform": "Select a platform", "Select plots": "Select plots", "Select products": "Select products", "Select server error": "Please select a server", "Select server": "Select a server", "Select soil analysis package to display data": "Select soil analysis package to display data", "Select subscription": "Please select a subscription from the plots list.", "Select stage": "Select stage", "Select state": "Select state", "Select sampling type": "Select sampling type", "Select time": "Select time", "Select tool": "Select tool", "Select tool and follow up screen instructions": "Select tool and follow up screen instructions", "Select type": "Select type", "Select year": "Select a year", "Select year error": "Please select a year", "Select unit": "Select unit", "Select calculation model": "Select calculation model", "Select value": "Select value", "Selected boundaries": "Selected boundaries", "Selected features": "Selected features", "Selected layer": "Selected layer", "Selected layers": "Selected layers", "Select VRA maps": "Select VRA maps", "Customize which columns to be added or press save to add all": "Customize which columns to be added or press save to add all.", "Select work operations": "Select work operations", "Selected fields": "Selected fields", "Select which columns to be copied": "Select which columns to be copied", "Send": "Send", "Send report": "Send report", "Separate email addresses with commas or press 'Enter' key": "Separate email addresses with commas or press 'Enter' key.", "Service manager": "Service manager", "Severity percent": "Severity percent", "set_active": "Status", "Set new": "Set new", "Settings": "Settings", "Settlement": "Settlement", "Short name": "Short name", "Show": "Show", "Show all": "Show all", "Show advanced": "Show advanced", "Show additional information": "Show additional information", "Show already included plots": "Show already included plots", "Show covered area": "Show covered area", "Show filtered only": "Show filtered only", "Show fiters": "Show fiters", "Show information": "Show information", "Show legend": "Show legend", "Show tasks": "Show tasks", "Show tasks by work operation": "Show tasks by work operation", "Show track": "Show track", "Show VRA maps": "Show VRA maps", "Shows the properties included in contracts with a period affecting the selected one or more farm years": "Shows the properties included in contracts with a period affecting the selected one or more farm years.", "Shows the properties included in contracts with a period expiring in the selected one or more farm years": "Shows the properties included in contracts with a period expiring in the selected one or more farm years.", "Shows the owned, leased or rented properties that can be leased to third party for the selected farm year. Properties that are already subleased for the selected year are excluded": "Shows the owned, leased or rented properties that can be leased to third party for the selected farm year. Properties that are already subleased for the selected year are excluded.", "Silking": "<PERSON>ing", "Single": "Single", "small": "small", "Soil": "Soil", "soil": "soil", "Soils": "Soils", "Soil by element": "Map of ", "Stem elongation-Ear formation": "Stem elongation-Ear formation", "Stem elongation-Silking": "Stem elongation-<PERSON><PERSON>", "Soil analysis": "Soil", "Soil layer, cm": "Soil layer, cm", "Soil Moisture": "Soil Moisture", "Soil Moisture Max": "Soil Moisture % Max", "Soil Moisture Min": "Soil Moisture % Min", "Soil samples results": "Soil tests results", "Soil Temperature Max": "Soil Temperature Max", "Soil Temperature Min": "Soil Temperature Min", "Sowing date": "Sowing date", "Speed": "Speed", "Split": "Split", "Spraying forecast": "Spraying forecast", "Standing duration": "Standing duration", "State gazette": "State gazette", "Start": "Start", "Start time": "Start time", "Start date": "Start date", "Station ID": "Station ID", "Station name": "Station name", "State": "State", "Stations": "Stations", "Status": "Status", "Status manager": "Organization manager", "Stem Elongation": "Stem Elongation", "Stem elongation": "Stem elongation", "Subject": "Subject", "Subject name": "Subject name", "Subscribed area": "Subscribed area", "Subscriptions": "Subscriptions", "Successful element state update": "Successfully updated the elements state.", "Successful element state update for field": "Successful element state update for field", "Successful plots upload": "{{fields}} fields, {{area}} uploaded successfully.", "Supported bans": "Supported bans", "Surveys": "Surveys", "Sync": "Sync", "Sync implements": "Sync implements", "Sync units": "Sync units", "Synchronize": "Synchronize", "Synchronized": "Synchronized", "System layers": "System layers", "System name": "System name", "Sprayer": "Sprayer", "Success": "Success", "Tap on screen": "Tap on screen", "Tap on screen to draw line points.": "Tap on screen to draw line points.", "Tap on screen to draw polygon points.": "Tap on screen to draw polygon points.", "Tap twice to end drawing.": "Tap twice to end drawing.", "Task": "Task", "Task states": "Task states", "Tasks": "Tasks", "Control panel": "Control panel", "Tasks by date": "Tasks by date", "Tasks by type": "Tasks by type", "Tasks timeline": "Tasks timeline", "Tasks products and costs report": "Tasks products and costs report", "tasks_products_and_costs_report": "Tasks products and costs report", "Tasks without field": "Tasks without field", "text": "text", "Tasseling": "Tasseling", "The contract end date is after the selected property change date": "The contract end date is after the selected property change date.", "The deletion was successful": "The deletion was successful!", "The feature will not be saved and you will be brought back to the main screen.": "The feature will not be saved and you will be brought back to the main screen.", "The fields events have been deleted successfully": "The fields events have been deleted successfully!", "The maximum number of integrations has been reached": "The maximum number of integrations has been reached!", "The scheduled report is deleted successfully": "The scheduled report is deleted successfully!", "The scheduled report is not deleted successfully": "The scheduled report is not deleted successfully!", "The scheduled report is updated successfully": "The scheduled report is updated successfully!", "The scheduled report is not updated successfully": "The scheduled report is not updated successfully!", "The unit of measure has been added successfully": "The unit of measure has been added successfully!", "The unit of measure has been deleted successfully": "The unit of measure has been deleted successfully!", "The unit of measure has been updated successfully": "The unit of measure has been updated successfully!", "The plot is not in subscription": "The plot is not in subscription", "The prescription has been copied succesfully": "The prescription has been copied succesfully!", "The prescription has been created succesfully": "The prescription has been created succesfully!", "The product has been added successfully": "The product has been added successfully!", "The product has been deleted successfully": "The product has been deleted successfully!", "The product has been updated successfully": "The product has been updated successfully!", "The product is used for nitrogen fertilization": "The product is used for nitrogen fertilization", "The task has been created successfully": "The task has been created successfully!", "The task has been updated successfully": "The task has been updated successfully!", "The task continues over time": "The task continues over time", "The task starts back in time": "The task starts back in time", "The task starts back in time and continues over time": "The task starts back in time and continues over time", "There are features selected from map. The filter will be applied only for the selected features and not for the entire layer!": "There are features selected from map. The filter will be applied only for the selected features and not for the entire layer!", "There are no columns to be copied": "There are no columns to be copied.", "There are no scheduled reports": "There are no scheduled reports", "There are no results for the selected criteria": "There are no results for the selected criteria", "There are no plots from the Diary falling within this physical block of EPORD": "There are no plots from the Diary falling within this physical block of EPORD", "There are fields which do not exist in the current KVS": "There are fields which do not exist in the current KVS", "There was an error while syncing fields": "There was an error while syncing fields!", "There was a problem retrieving the data. Please, try again.": "There was a problem retrieving the data. Please, try again.", "Third leaf": "Third leaf", "Tilth": "Tilth", "Main shoot and 4 tillers": "Main shoot and 4 tillers", "Main shoot and 1 tiller": "Main shoot and 1 tiller", "Main shoot and 2 tillers": "Main shoot and 2 tillers", "Temperature": "Temperature", "Tillering": "<PERSON><PERSON>", "Tillering-Stem Elongation": "Tillering-Stem Elongation", "Time": "Time", "to": "to", "To create a similar prescription, you need to change at least one field.": "To create a similar prescription, you need to change at least one field.", "To measure new boundary on the field tap the 'Measure area' button on the map": "To measure new boundary on the field tap the 'Measure area' button on the map", "To transfer boundaries from 'Mobile layer' from the desktop app press Sync": "To transfer boundaries from 'Mobile layer' from the desktop app press Sync", "Token": "Token", "Total": "Total", "Total area for layers": "Total area for layers", "Total groups": "Total groups", "Total machines": "Total machines", "Total trailers": "Total trailers", "Total pivots": "Total pivots", "Total platforms": "Total platforms", "Total tasks": "Total tasks", "Total Area": "Total Area", "Total irrigated area": "Total irrigated area", "Total items": "Total items", "Total area": "Total area", "Total consumption": "Total consumption", "Total distance": "Total distance", "Total fuel": "Total fuel", "Total cost": "Total cost", "Total count": "Total count", "TotalCost": "Total cost", "Total qty": "Total qty", "Total Nitrogen (dka)": "Total Nitrogen (dka)", "TotalQTY": "Total qty", "Total N rate": "Total N rate", "ton": "ton", "Transparency": "Transparency", "Tracing": "Tracing", "Track": "Track", "Track length": "Track length", "Traveled distance": "Traveled distance", "Trade name": "Trade name", "true": "yes", "Type": "Type", "Type of coloring": "Type of coloring", "Type of contract": "Type of contract", "Type of product": "Type of product", "Type part of the value in the input and select from the list. You can select multiple values": "Type part of the value in the input and select from the list. You can select multiple values.", "Type HP": "Type HP", "Type PPP": "Type PPP", "Tractor": "Tractor", "Truck": "Truck", "Upcoming": "Upcoming", "Update": "Update", "Update properties": "Update properties", "Update contracts": "Update contracts", "underdeclared": "underdeclared", "Unit ID": "Unit ID", "Unit": "Unit", "Unit already exists": "Unit already exists", "Unit per area": "Unit per area", "Unit value": "Unit value", "Units": "Units", "Units of measure": "Units of measure", "Upload": "Upload", "Upload failed": "Upload failed. Please, try again.", "Upload fields": "Upload fields", "Upload files": "Upload files", "upload_plots_info_1": "Click or drag KML or .zip files here.", "upload_plots_info_2": "SHP format consist of three main files .shp, .shx and .dbf. Select all three, put them in ZIP archive and upload it.", "Uploaded date": "Uploaded date", "Use device location": "Use device location", "Used/Total area": "Used/Total area", "Used area": "Used area", "Used by": "Used by", "Usage rights as of date": "Usage rights as of date", "Usage rights (OSZ)": "Usage rights (OSZ)", "Calculation model": "Calculation model", "Calculation model is required.": "Calculation model is required.", "CAD": "CAD", "Username": "Username", "User": "User", "User address": "User address", "User EKATTE": "User EKATTE", "User EIK/PID": "User EIK/PID", "User phone": "User phone", "Use GPS only": "Use GPS only", "Valid boundaries": "Valid boundaries", "Value": "Value", "VRA (Substance)": "VRA (Substance)", "VRA (Product)": "VRA (Product)", "VRA map": "VRA map", "VRA maps": "VRA maps", "Vegetation": "Vegetation", "Vegetative Growth Stages": "Vegetative Growth Stages", "Visible": "Visible", "Volume": "Volume", "Volume density coefficient": "Volume density coefficient", "Volume density coefficient is required.": "Volume density coefficient is required.", "VPS type": "VPS type", "Water amount": "Water amount", "Wax maturity": "Wax maturity", "Weather Station History": "Weather Station History", "Weather data": "Weather data", "Weather": "Weather", "Weather forecast": "Weather forecast", "Weather stations": "Weather stations", "Weather stations report": "Weather stations report", "Weight": "Weight", "Wind speed": "Wind speed", "Will": "Will", "With VRA": "With VRA", "White spot": "White spot", "Wish": "Wish", "with_contracts": "with contracts", "without_contracts": "without contracts", "Without VRA": "Without VRA", "without": "without", "Without": "Without", "Warehouse for plant production": "Warehouse for plant production", "XLSX": "XLSX", "Year": "Year", "Yield": "Yield", "Yields": "Yields", "Yes": "Yes", "You cannot edit attribute information and geometry simultaneously": "You cannot edit attribute information and geometry simultaneously", "You have already created a task with the same combination of field, work operations and period": "You have already created a task with the same combination of field, work operations and period!", "You have already created a task with the same combination of work operations and period": "You have already created a task with the same combination of work operations and period!", "You have not added any guidance lines to this task, yet": "You have not added any guidance lines to this task, yet", "You have not added any products to this task, yet": "You have not added any products to this task, yet", "You have not added any VRA maps to this task, yet": "You have not added any VRA maps to this task, yet", "You have not added any work operations to this task, yet": "You have not added any work operations to this task, yet", "You have not selected a driver to this task, yet": "You have not selected a driver to this task, yet", "You have not selected a field to this task, yet": "You have not selected a field to this task, yet", "You have not selected a machine to this task, yet": "You have not selected a machine to this task, yet", "You have not selected an implement to this task, yet": "You have not selected an implement to this task, yet", "You have reached your EKATTE limit. Please contact your account manager to increase the available amount of EKATTE": "You have reached your EKATTE limit. Please contact your account manager to increase the available amount of EKATTE.", "You have selected too much data, please increase the reporting time or reduce the preview time": "You have selected too much data, please increase the reporting time or reduce the preview time.", "You have to select at least one active ingredient": "You have to select at least one active ingredient.", "Your contracts are up to date": "Your contracts are up to date.", "Zoom to": "Zoom to", "Zoom to extent": "Zoom to extent", "This page doesn't exist": "This page doesn't exist", "available": "available", "item results": "item", "items results": "items", "of": "of", "points_above_average": "{{value}} points above average", "points_below_average": "{{value}} points below average", "Cannot merge geometries": "Cannot merge geometries", "Cannot save more than {{maxPhotosNum}} photos at once!": "Cannot save more than {{maxPhotosNum}} photos at once!", "pc.": "pc.", "unit": "unit", "updated integration": "updated integration", "Updated plots": "Updated plots", "Linked accounts": "Linked accounts", "litre": "litre", "LUT": "LUT", "Show search": "Show search", "gallon": "gallon", "Gridded": "Gridded", "Selected": "Selected", "Sampling": "Sampling", "Sampling date": "Sampling date", "Sampling type": "Sampling type", "Sampling task will be updated and has to be synced again on the mobile sampling app!": "Sampling task will be updated and has to be synced again on the mobile sampling app!", "Sampled": "Sampled", "Select grid cell value": "Select grid cell value", "Grid generation": "Grid generation...", "Custom from file": "Custom from file", "Please mark the desired plots with a check mark in front of them": "Please mark the desired plots with a check mark in front of them", "Please upload file for the grid.": "Please upload file for the grid.", "Please select a type": "Please select a type", "Please enter a rate": "Please enter a rate", "Please enter a default price": "Please enter a default price", "Please select a state": "Please select a state", "Please select an unit": "Please select an unit", "Please select an unit per area": "Please select an unit per area", "Important": "Important", "The geometry files must have": "The geometry files must have", "as attributes": "as attributes", "Grid": "Grid", "Group elements": "Group elements", "fertiliser": "fertiliser", "fertilizer": "fertilizer", "fertilizers": "fertilizers", "Fertilization type": "Fertilization type", "Fertilization type is required.": "Fertilization type is required.", "file": "file", "File": "File", "fuel": "fuel", "false": "no", "Points": "Points", "Browse": "Browse", "Brief comments based on the results obtained": "Soil tests results comments", "Custom": "Custom", "Custom cell area": "Custom cell area", "pivot": "pivot", "pivots": "pivots", "Pivots": "Pivots", "Pivots position": "Pivots position", "ppp": "ppp", "PPP": "PPP", "Previous value": "Previous value", "Current value": "Current value", "updated station": "updated station", "No data": "No data", "NoData": "No data", "No data for requested cadastral property": "No data for requested cadastral property", "No data for selected criteria": "No data for selected criteria", "No Internet connection": "No Internet connection!", "No intersection layer available! Please select a layer to intersect from the main menu.": "No intersection layer available! Please select a layer to intersect from the main menu.", "none": "none", "Nitrogen content (N)": "Nitrogen content (N)", "Spanish": "Spanish", "Romanian": "Romanian", "Idle": "Idle", "Off": "Off", "Off farm": "Off farm", "Offline": "Offline", "Moving": "Moving", "Select machine": "Select machine", "seeds": "seeds", "irrigation": "irrigation", "off": "Off", "others": "others", "movement": "Movement", "pressure alarm": "Pressure alarm", "pressure unknown": "Pressure unknown", "Predecessor": "Predecessor", "position unknown": "Position unknown", "speed alarm": "Speed alarm", "platforms": "platforms", "machines": "machines", "PressureAlarm": "Pressure alarm", "PressureUnknown": "Pressure\nunknown", "PositionUnknown": "Position\nunknown", "SpeedAlarm": "Speed alarm", "MisalignmentAlarm": "Safety аlarm", "Warning": "Warning", "Warning Pressure": "Warning pressure", "Warning Reading Sensors": "Warning reading sensors", "Movement": "Movement", "Width": "<PERSON><PERSON><PERSON>", "Work operation": "Work operation", "Work operations": "Work operations", "Last15min": "Last 15 minutes", "Last1h": "Last 1 hour", "Last15minShort": "Last 15 min", "Last1hShort": "Last 1h", "The logged in user doesn't have access to the farms of the selected organization.": "The logged in user doesn't have access to the farms of the selected organization.", "Category": "Category", "Variety/Hybrid": "Variety/Hybrid", "Today": "Today", "Yesterday": "Yesterday", "Last week": "Last week", "Last two weeks": "Last two weeks", "This month": "This month", "Last month": "Last month", "Farm year": "Farm year", "Ukrainian": "Ukrainian", "Italian": "Italian", "Russian": "Russian", "dka": "dka", "(dka)": "(dka)", "дка": "dka", "h": "h", "m": "m", "s": "s", "ha": "ha", "HA": "ha", "l": "l", "litreShort": "l", "km": "km", "km/h": "km/h", "kg": "kg", "kg/dka": "kg/dka", "kg/l": "kg/l", "l/dka": "l/dka", "ml/dka": "ml/dka", "g/dka": "g/dka", "g/kg": "g/kg", "g/l": "g/l", "metre": "metre", "mm": "mm", "centimetre": "centimetre", "Class.VL": "VL", "Class.very low": "very low", "Class.very low.slug": "VL", "Class.L": "L", "Class.low": "low", "Class.low.slug": "L", "Class.S": "S", "Class.sufficient": "sufficient", "Class.sufficient.slug": "S", "Class.M": "M", "Class.medium": "medium", "Class.medium.slug": "M", "Class.G": "G", "Class.good": "good", "Class.good.slug": "G", "Class.H": "H", "Class.high": "high", "Class.high.slug": "H", "Class.VH": "VH", "Class.very high": "very high", "Class.very high.slug": "VH", "Class.UAC": "UAC", "Class.ultra acidic": "ultra acidic", "Class.ultra acidic.slug": "UAC", "Class.EAC": "EAC", "Class.extremely acidic": "extremely acidic", "Class.extremely acidic.slug": "EAC", "Class.VSC": "VSC", "Class.very strongly acidic": "very strongly acidic", "Class.very strongly acidic.slig": "VSC", "Class.SAC": "SAC", "Class.strongly acidic": "strongly acidic", "Class.strongly acidic.slug": "SAC", "Class.MAC": "MAC", "Class.moderately acidic": "moderately acidic", "Class.moderately acidic.slug": "MAC", "Class.SLA": "SLA", "Class.slightly acidic": "slightly acidic", "Class.slightly acidic.slug": "SLA", "Class.NEU": "NEU", "Class.neutral": "neutral", "Class.neutral.slug": "NEU", "Class.SAL": "SAL", "Class.slightly alkaline": "slightly alkaline", "Class.slightly alkaline.slug": "SAL", "Class.MAL": "MAL", "Class.moderately alkaline": "moderately alkaline", "Class.moderately alkaline.slug": "MAL", "Class.STA": "STA", "Class.strongly alkaline": "strongly alkaline", "Class.strongly alkaline.slug": "STA", "Class.VSA": "VSA", "Class.very strongly alkaline": "very strongly alkaline", "Class.very strongly alkaline.slug": "VSA", "Class.AC": "AC", "Class.acidic": "acidic", "Class.AC.slug": "AC", "Class.AL": "AL", "Class.alcaline": "alcaline", "Class.AL.slug": "AL", "pH": "pH", "NO3-N": "NO<sub>3</sub>-N", "NH4-N": "NH<sub>4</sub>-N", "TMN": "TMN", "P2O5": "P<sub>2</sub>O<sub>5</sub>", "K2O": "K<sub>2</sub>O", "CaO": "CaO", "MgO": "MgO", "Na2O": "Na<sub>2</sub>O", "S": "S", "B": "B", "Cu": "<PERSON><PERSON>", "Fe": "Fe", "Mn": "Mn", "Zn": "Zn", "Мо": "Мо", "N": "N", "Total Carbon": "Total Carbon", "Organic Carbon": "Organic Carbon", "Inorganic Carbon": "Inorganic Carbon", "Total Carbonates": "Total Carbonates", "Active carbonates": "Active carbonates", "EC": "EC", "LeafN": "LeafN", "LeafP": "LeafP", "LeafK": "LeafK", "LeafCa": "LeafCa", "LeafMg": "LeafMg", "LeafNa": "LeafNa", "LeafS": "LeafS", "LeafFe": "LeafFe", "LeafCu": "LeafCu", "LeafZn": "LeafZn", "LeafMn": "LeafMn", "LeafB": "LeafB", "OM (LOI)": "Organic Matter (LOI)", "OM (Dumas)": "Organic Matter (Dumas)", "Active carbon": "Active carbon", "CEC": "CEC", "BS": "BS", "K+": "K+", "Ca2+": "Ca<sup>2+</sup>", "Mg2+": "Mg<sup>2+</sup>", "Na+": "Na+", "H+": "H+", "Analyses.pH": "pH", "Analyses.NO3-N mg/kg": "NO<sub>3</sub>-N mg/kg", "Analyses.NH4-N mg/kg": "NH<sub>4</sub>-N mg/kg", "Analyses.TMN mg/kg": "TMN mg/kg", "Analyses.P2O5 mg/kg": "P<sub>2</sub>O<sub>5</sub> mg/kg", "Analyses.K2O mg/kg": "K<sub>2</sub>O mg/kg", "Analyses.CaO mg/kg": "CaO mg/kg", "Analyses.MgO mg/kg": "MgO mg/kg", "Analyses.Na2O mg/kg": "Na<sub>2</sub>O mg/kg", "Analyses.S mg/kg": "S mg/kg", "Analyses.B mg/kg": "B mg/kg", "Analyses.Cu mg/kg": "Cu mg/kg", "Analyses.Fe mg/kg": "Fe mg/kg", "Analyses.Mn mg/kg": "Mn mg/kg", "Analyses.Zn mg/kg": "Zn mg/kg", "Analyses.Мо mg/kg": "Мо mg/kg", "Analyses.N": "N", "Results.pH": "pH", "Results.NO3-N": "NO<sub>3</sub>-N", "Results.NH4-N": "NH<sub>4</sub>-N", "Results.TMN": "N", "Results.P2O5": "P<sub>2</sub>O<sub>5</sub>", "Results.K2O": "K<sub>2</sub>O", "Results.CaO": "CaO", "Results.MgO": "MgO", "Results.Na2O": "Na<sub>2</sub>O", "Results.S": "S", "Results.B": "B", "Results.Cu": "<PERSON><PERSON>", "Results.Fe": "Fe", "Results.Mn": "Mn", "Results.Zn": "Zn", "Results.Мо": "Мо", "Results.N": "N", "Results.Add_N_fall": "N fall", "Results.Add_N_total": "N sping/summer", "Results.Add_P_total": "P", "Results.Add_K_total": "K", "Results.Add_MgO_total": "MgO", "Results.Add_CaO_total": "CaO", "Results.Add_S_total": "S", "Calcium-ammonium nitrate - 26% (CAN)": "Calcium-ammonium nitrate - 26% (CAN)", "Ammonium nitrate - 34%": "Ammonium nitrate - 34%", "Urea - 46%": "Urea - 46%", "Urea ammonium nitrate - 32% (UAN)": "Urea ammonium nitrate - 32% (UAN)", "Urea ammonium sulfate - 40%": "Urea ammonium sulfate - 40%", "Unit Imei": "Unit Imei", "Ammonium sulfate - 21%": "Ammonium sulfate - 21%", "Sodium nitrate - 15%": "Sodium nitrate - 15%", "Potassium nitrate - 13%": "Potassium nitrate - 13%", "Calcium nitrate - 15%": "Calcium nitrate - 15%", "Magnesium nitrate - 10%": "Magnesium nitrate - 10%", "Phosphorus flour – 30%": "Phosphorus flour – 30%", "Bone meal – 34%": "Bone meal – 34%", "Simple superphosphate – 20%": "Simple superphosphate – 20%", "Double superphosphate – 40%": "Double superphosphate – 40%", "Triple superphosphate – 46%": "Triple superphosphate – 46%", "Monoammonium phosphate – 52% (MAP)": "Monoammonium phosphate – 52% (MAP)", "Diammonium phosphate – 46% (DAP)": "Diammonium phosphate – 46% (DAP)", "NPK - 10%": "NPK – 10%", "NPK - 15%": "NPK – 15%", "NPK - 16%": "NPK – 16%", "NPK – 20%": "NPK – 20%", "Potassium chloride – 60% (KCI)": "Potassium chloride – 60% (KCI)", "Potassium sulphate – 50% (K2SO4)": "Potassium sulphate – 50% (K2SO4)", "Potassium nitrate – 46.5% (KNO3)": "Potassium nitrate – 46.5% (KNO3)", "Potassium magnesium sulfate – 30% (KMgSO4)": "Potassium magnesium sulfate – 30% (KMgSO4)", "Created on": "Created on", "Select product": "Select product", "Not active": "Not active", "Not participating in scheme": "Not participating in scheme", "Alert": "<PERSON><PERSON>", "Misalignment": "Misalignment", "Solar radiation": "Solar radiation", "Battery": "Battery", "Leaf wetness": "Leaf wetness", "Air": "Air", "Air humidity": "Air humidity", "Air pressure": "Air pressure", "Air temperature": "Air temperature", "Air temperature -6cm": "Air temperature -6cm", "Humidity": "<PERSON><PERSON><PERSON><PERSON>", "Soil temperature": "Soil temperature", "Soil temperature -12cm": "Soil temperature -12cm", "Soil moisture": "Soil moisture", "Machines tasks reports": "Machines tasks reports", "machines_tasks_report": "Machines tasks report", "machines_events_report": "Machines events report", "LastWeek": "last week", "ThisMonth": "this month", "Daily": "daily", "daily": "daily", "Weekly": "weekly", "Monthly": "monthly", "monthly": "monthly", "start_of_month": "start of month", "end_of_month": "end of month", "this_month": "this month", "last_week": "last week", "today": "today", "yesterday": "yesterday", "Mo": "Mo", "Tu": "Tu", "We": "We", "Th": "Th", "Fr": "Fr", "Sa": "Sa", "Su": "Su", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday", "Mon": "Mon", "Tue": "<PERSON><PERSON>", "Wed": "Wed", "Thu": "<PERSON>hu", "Fri": "<PERSON><PERSON>", "Sat": "Sat", "Sun": "Sun", "Jan": "Jan", "Feb": "Feb", "Mar": "Mar", "Apr": "Apr", "May": "May", "Jun": "Jun", "Jul": "Jul", "Aug": "Aug", "Sept": "Sept", "Oct": "Oct", "Nov": "Nov", "Dec": "Dec", "VRA map saved": "VRA map saved!", "Failed attempt to change cell status!": "Failed attempt to change cell status!", "Failed to load machines": "Failed to load machines!", "Failed to load ingredients": "Failed to load ingredients!", "Failed to load markers": "Failed to load markers!", "Failed to load platforms": "Failed to load platforms!", "Failed to load weather stations": "Failed to load weather stations!", "Failed to save prescription": "Failed to save prescription!", "Failed to update prescription": "Failed to update prescription!", "Failed to save VRA map": "Failed to save VRA map!", "Failed to update properties": "Failed to update properties!", "Failed plots": "Failed plots", "Failed element state update for field": "Failed element state update for field", "Failed to delete field": "Failed to delete field", "Failed to delete layer": "Failed to delete layer", "Failed sampling task update!": "Failed sampling task update!", "NDVI_N": "NDVI-N", "All farms": "All farms", "All": "All", "Slug.NO3-N": "NO3", "Slug.NH4-N": "NH4", "Slug.Total Carbon": "Ctot", "Slug.Organic Carbon": "Organic Carbon", "Slug.Inorganic Carbon": "Cino", "Slug.Total Carbonates": "Tcar", "Slug.Active carbonates": "Acar", "Slug.EC": "EC", "Slug.LeafN": "vgN", "Slug.LeafP": "vgP", "Slug.LeafK": "vgK", "Slug.LeafCa": "vgCa", "Slug.LeafMg": "vgMg", "Slug.LeafNa": "vgNa", "Slug.LeafS": "vgS", "Slug.LeafFe": "vgFe", "Slug.LeafCu": "vgCu", "Slug.LeafZn": "vgZn", "Slug.LeafMn": "vgMn", "Slug.LeafB": "vgB", "Slug.Na2O": "Na2O", "Slug.CEC": "CEC", "Slug.BS": "BS", "Slug.K+": "K+", "Slug.Ca2+": "Ca2+", "Slug.Mg2+": "Mg2+", "Slug.Na+": "Na+", "Slug.H+": "H+", "Slug.pH": "pH", "Slug.TMN": "TMN", "Slug.P2O5": "P2O5", "Slug.K2O": "K2O", "Slug.CaO": "CaO", "Slug.MgO": "MgO", "Slug.S": "S", "Slug.Cu": "<PERSON><PERSON>", "Slug.Mn": "Mn", "Slug.Zn": "Zn", "Slug.B": "B", "Slug.OM (LOI)": "OM (LOI)", "Slug.Fe": "Fe", "Slug.Mo": "Mo", "Slug.Humus": "<PERSON><PERSON>", "Area by class": "Area by class", "Work": "Work", "Transportation": "Transport", "WorkOutsidePlot": "Work outside plot", "Unknown": "Unknown", "URN": "URN", "Fertilizing": "Fertilizing", "Fertilizer": "Fertilizer", "Fertilizers": "Fertilizers", "SowingAndPlanting": "Sowing and Planting", "CropProtection": "Crop protection", "Tillage": "Tillage", "Baling": "<PERSON><PERSON>", "Mowing": "Mowing", "Wrapping": "Wrapping", "Harvesting": "Harvesting", "NoWorkOperations": "No work operations", "ForageHarvesting": "Forage harvesting", "Transport": "Transport", "Swathing": "Swathing", "Subsoiling": "Subsoiling", "Rotary": "Rotary", "Chopping": "Chopping", "CollectedDripLines": "Collected drip lines", "InstallingDripIrrigation": "Installing drip irrigation", "RotaryReRigging": "Rotary re-rigging", "WindroverHarvest": "Windrover harvest", "Plowing": "Plowing", "Spraying1": "Spraying 1", "Spraying2": "Spraying 2", "Spraying3": "Spraying 3", "Spraying4": "Spraying 4", "Spraying5": "Spraying 5", "Spraying6": "<PERSON><PERSON><PERSON> 6", "Spraying7": "<PERSON>praying 7", "Spraying8": "Spraying 8", "Spraying9": "Spraying 9", "Spraying10": "Spraying 10", "Spraying11": "Spraying 11", "Spraying12": "Spraying 12", "Fertilization1": "Fertilization 1", "Fertilization2": "Fertilization 2", "Fertilization3": "Fertilization 3", "Fertilization4": "Fertilization 4", "Farm name": "Farm name", "Marker name": "Marker name", "Make a photo": "Make a photo", "Choose from gallery": "Choose from gallery", "24 h": "24 h", "1 h": "1 h", "15 minutes": "15 minutes", "Coordinates copied": "Coordinates copied", "General information successfully updated": "General information successfully updated!", "Agrimi works best with Location Services turned on.": "<PERSON><PERSON><PERSON> works best with Location Services turned on.", "The location service will help you to see your position on the map.": "The location service will help you to see your position on the map.", "Turn On in Settings": "Turn On in Settings", "Error uploading marker image. Only files with the following extensions are allowed: jpeg, gif, png.": "Error uploading marker image. Only files with the following extensions are allowed: jpeg, gif, png.", "Cells in": "Cells in", "Cells selected": "Cells selected", "Plots status": "Plots status", "Plots state": "Plots state", "Send for sampling": "Send for sampling", "The status of the plots has been changed to “For sampling”": "The status of the plots has been changed to “For sampling”", "1st day of month": "1st day of month", "Last day of month": "Last day of month", "By day": "By day", "Measure area": "Measure area", "Measuring area": "Measuring area", "Measure distance": "Measure distance", "Measuring distance": "Measuring distance", "Zoom to plot": "Zoom to plot", "Filter by upload date": "Filter by upload date", "Enter plot name": "Enter plot name", "Report language": "Report language", "Wind speed max": "Wind speed max", "Air temperature 0cm": "Air temperature 0cm", "Soil temperature -6cm": "Soil temperature -6cm", "Custom rate": "Custom rate", "To change the rate for specific zone, select it from the map and enter the new value.": "To change the rate for specific zone, select it from the map and enter the new value.", "Select points": "Select points", "Guidance line": "Guidance line", "Guidance lines": "Guidance lines", "Add line": "Add line", "New guidance line": "New guidance line", "Edit guidance line": "Edit guidance line", "Cannot project lines of type Headland": "Cannot project lines of type Headland", "Start point": "Start point", "End point": "End point", "Angle against North": "Angle against North", "Shift line": "Shift line", "Meters": "Meters", "Direction": "Direction", "Profile": "Profile", "Project line": "Project line", "Offset width of the projected lines": "Offset width of the projected lines", "Please select points from the map": "Please select points from the map", "The value is out of bounds": "The value must be between {{min}} and {{max}}", "Invalid headline": "Invalid headline", "Error saving guidance line": "Error saving guidance line", "Click on map for start and end points": "Click on map for start and end points", "Click on map to select, press Apply to filter only selected features": "Click on map to select, press Apply to filter only selected features", "Click on map to draw polygon.": "Click on map to draw polygon.", "Click on the map to select objects and press Apply to add the selected objects to the filter": "Click on the map to select objects and press Apply to add the selected objects to the filter", "Show on map": "Show on map", "Edit line": "Edit line", "Copy line": "Copy line", "Export line": "Export line", "line": "line", "lines": "lines", "Show guidance lines": "Show guidance lines", "No lines to show": "No lines to show", "Invalid headland": "Invalid headland", "Delete events": "Delete events", "Delete line": "Delete line", "Error deleting guidance line": "Error deleting guidance line", "Error exporting guidance lines": "Error exporting guidance lines", "Export all lines": "Export all lines", "Export all lines?": "Export all {{guidanceLinesCount}} lines?", "Export selected lines": "Export selected lines", "Export all VRA maps": "Export all VRA maps", "Export all VRA maps?": "Export all {{vraMapsCount}} VRA maps", "Export selected VRA maps": "Export selected VRA maps", "Export for ISAK": "Export for ISAK", "Export for Trimble AgGPS (Common File)": "Export for Trimble AgGPS (Common File)", "Export for Trimble AgGPS (Separate files)": "Export for Trimble AgGPS (Separate files)", "Export for Trimble AgData (Common File)": "Export for Trimble AgData (Common File)", "Export for Trimble AgData (Separate files)": "Export for Trimble AgData (Separate files)", "Export for Topcon (Common File)": "Export for Topcon (Common File)", "Export for TopcTon (Separate Files)": "Export for TopcTon (Separate Files)", "Export for Mueller Track Guide I and II (before 10.2014) (Common File)": "Export for Mueller Track Guide I and II (before 10.2014) (Common File)", "Export for Mueller Track Guide I and II (before 10.2014) (Separate Files)": "Export for Mueller Track Guide I and II (before 10.2014) (Separate Files)", "Export for Mueller Track Guide I and II (after 10.2014 and before 2017 (Common File)": "Export for Mueller Track Guide I and II (after 10.2014 and before 2017 (Common File)", "Export for Mueller Track Guide I and II (after 10.2014 and before 2017 (Separate Files)": "Export for Mueller Track Guide I and II (after 10.2014 and before 2017 (Separate Files)", "Export for Mueller Track Guide II and III (post-2017) (Common File)": "Export for Mueller Track Guide II and III (post-2017) (Common File)", "Export for Mueller Track Guide II and III (post-2017) (Separate Files)": "Export for Mueller Track Guide II and III (post-2017) (Separate Files)", "Export for KML": "Export for KML", "Export to KML (for Farm Track)": "Export to KML (for Farm Track)", "Export for another GPS": "Export for another GPS", "Export for John Deere(Common File)": "Export for <PERSON>(Common File)", "Export for John Deere(Separate Files)": "Export for <PERSON>(Separate Files)", "Export all fields": "Export all fields", "Export only fields with events": "Export only fields with events", "Select products from the list": "Select products from the list", "Open gallery": "Open gallery", "IMEI number": "IMEI number", "IMEI": "IMEI", "Station type": "Station type", "Station radius": "Station radius", "Station IMEI": "Station IMEI", "Cannot edit station type": "Cannot edit station type", "Cannot edit station imei": "Cannot edit station IMEI", "Station coordinates not found. Please check station type and IMEI.": "Station coordinates not found. Please check station type and IMEI.", "No active Weather stations package.": "No active Weather stations package.", "No more stations allowed to create for this contract.": "No more stations allowed to create for this contract.", "Station already exists.": "Station already exists.", "Station already exists for the organization.": "Station already exists for the organization.", "Station not found. Please check station type and IMEI.": "Station not found. Please check station type and IMEI.", "Reports for approved tasks and products and costs will be updated": "Reports for approved tasks and products and costs will be updated", "There are recorded machines and irrigation tasks for this field, which will be deleted!": "There are {{machineEvents}} recorded  machines and {{irrigationEvents}} irrigation tasks for this field, which will be deleted!", "There are fields with machines and irrigation tasks, which will be deleted too!": "There are fields with machines and irrigation tasks, which will be deleted too!", "Selected plots": "Selected plots", "Successfully removed plots": "{{fields}} fields, {{area}} removed successfully.", "Failed to remove plots. Please, try again.": "Failed to remove plots. Please, try again.", "Add fields": "Add fields", "Remove fields": "Remove fields", "Min length error": "The minimum number of characters is {{minLength}}.", "Max length errror": "The maximum number of characters is {{maxLength}}.", "Min number error": "The minimum number is {{minN<PERSON>ber}}.", "Max number error": "The maximum number is {{maxNumber}}.", "Max number of applications": "Max number of applications", "Maximum amount per dka": "Maximum amount per dka", "Maximum amount per dka for scheme": "Maximum amount per dka for scheme", "Estimated number of turns": "Estimated number of turns", "Estimated average length of pass": "Estimated average length of pass", "Cadastre": "Cadastre", "Zone name": "Zone name", "ByOwnersName": "By owners name", "ByTenantsName": "By tenants name", "ByAgreement": "By agreement", "ByCategory": "By category", "ByNTP": "By NTP", "ByOwnership": "By ownership", "Manage machines and products": "Manage machines and products", "Manage irrigation monitoring": "Manage irrigation monitoring", "Import new layer": "Import new layer", "Select files or drag here": "Select files or drag here", "Shape files in ZIP format are accepted": "Shape files in ZIP format are accepted", "Unsupported file format!": "Unsupported file format!", "Source data": "Source data", "Destination farm": "Destination farm", "Destination year": "Destination year", "Select the type of the data which is imported. The files will be automatically validated and imported based on the selected type.": "Select the type of the data which is imported. The files will be automatically validated and imported based on the selected type.", "Got it": "Got it", "Files history": "Files history", "Info": "Info", "Uploaded file info": "Uploaded file info", "File name": "File name", "File was uploaded successfully": "File was uploaded successfully", "File is processing": "File is processing", "File is uploaded, waiting for definition": "File is uploaded, waiting for definition", "File not uploaded": "File not uploaded", "Farming": "Farming", "Select column mapping": "Select column mapping", "Region": "Region", "Registration date": "Registration date", "Registration number": "Registration number", "number": "number", "Number": "Number", "Usage type": "Usage type", "Local area": "Local area", "Documented area": "Documented area", "Crop code": "Crop code", "Error saving layer column mapping": "Error saving layer column mapping", "Error uploading file": "Error uploading file '{{filename}}'", "Create layer from Excel": "Create layer from Excel", "Zip and XLSX files are accepted": "Zip and XLSX files are accepted", "Add hole": "Add hole", "Merge geometries": "Merge geometries", "Split geometry": "Split geometry", "A hole cannot intersect more than one geometry!": "A hole cannot intersect more than one geometry!", "Error saving geometry": "Error saving geometry", "Field name is required": "Field name is required", "Selected fields cannot be merged": "Selected fields cannot be merged", "Intersect layers": "Intersect layers", "Please select intersection layer": "Please select intersection layer", "Show labels": "Show labels", "Show only borders": "Show only borders", "Use label": "Use label", "Print size": "Print size", "Print layers": "Print layers", "Print only visible area": "Print only visible area", "Please select print settings and press 'Preview'.": "Please select print settings and press 'Preview'.", "Invalid geometry": "Invalid geometry", "Calculate average slope": "Calculate average slope", "Error calculating average slope": "Error calculating average slope", "Slope attribute updated": "Slope attribute updated", "Click to calculate slope": "Click to calculate slope", "Slope": "Slope", "Error calculating slope": "Error calculating slope", "There are no subscriptions": "There are no subscriptions", "There is no subscription for this user. Get in touch to learn more.": "There is no subscription for this user. Get in touch to learn more.", "Contact us": "Contact us", "Please use the following search format: EKKATE.REGION.NUMBER (example: 54321.1.1)": "Please use the following search format: EKKATE.REGION.NUMBER (example: 54321.1.1)", "Error deleting fields boundaries": "Error deleting fields boundaries", "Are you sure you want to delete the selected fields?": "Are you sure you want to delete the selected fields?", "Base map": "Base map", "Please select plots to delete": "Please select plots to delete", "No map layers selected yet": "No map layers selected yet", "Great, you've selected map layers. Now select a specific layer.": "Great, you've selected map layers.\nNow select a specific layer.", "Please select work operations!": "Please select work operations!", "Please enter implement width!": "Please enter implement width!", "Error loading farms": "Error loading farms", "Create VRA map": "Create VRA map", "Decline recommendation": "Decline recommendation", "Please write a decline reason...": "Please write a decline reason...", "Date of validity": "Date of validity", "The date of validity is required.": "The date of validity is required.", "An error occurred while loading the recommendation history": "An error occurred while loading the recommendation history", "updated the recommendation at": "updated the recommendation at", "created the recommendation at": "created the recommendation at", "For approve": "For approve", "Declined": "Declined", "Delivered": "Delivered", "crop": "Planned crop", "status": "Status", "targetYield": "Target yield, kg/", "validFrom": "Date of validity", "Variety": "Variety", "humus": "Humus content, %", "samplingTypes": "Sampling types", "declineReason": "Decline reason", "model": "Calculation model", "Index": "Index", "index": "index", "Edit recommendation": "Edit recommendation", "Error loading attribute info": "Error loading attribute info", "Select column": "Select column", "New layer": "New layer", "This field is required when farm is selected": "This field is required when farm is selected", "This field must not be empty": "This field must not be empty", "Copy of": "Copy of", "Export for GPX": "Export for GPX", "Error loading recommendation data": "Error loading recommendation data", "The fields have been copied successfully": "The fields have been copied successfully", "The fields have been clipped successfully": "The fields have been clipped successfully", "You have no diaries created yet": "You have no diaries created yet", "New diary": "New diary", "Administrative diary": "Administrative diary", "Create diary": "Create diary", "Edit diary": "Edit diary", "Farm address": "Farm address", "Please select": "Please select", "EGN": "EGN", "EIK": "EIK", "An error occurred while loading the layers": "An error occurred while loading the layers", "The EKATTE code must be 5 digits long and is used to populate the diary": "The EKATTE code must be 5 digits long and is used to populate the diary", "The crop can be a text field or 6-digit code representing the SFA nomenclature": "The crop can be a text field or 6-digit code representing the SFA nomenclature", "The identifier represents the name of the field": "The identifier represents the name of the field", "For layers of type \"Work\", you need to select which columns to use for filling the EKATTE, crop and identifier in the diary.": "For layers of type \"Work\", you need to select which columns to use for filling the EKATTE, crop and identifier in the diary.", "If this data is not filled, you must first fill it in Administrative map.": "If this data is not filled, you must first fill it in Administrative map.", "The diary has been created successfully": "The diary has been created successfully", "Error creating diary": "Error creating diary", "An error occurred while loading the diaries": "An error occurred while loading the diaries", "Your diaries": "Your diaries", "Are you sure you want to delete this diary?": "Are you sure you want to delete this diary?", "An error occurred while loading the form data": "An error occurred while loading the form data", "The diary has been updated successfully": "The diary has been updated successfully", "Error updating diary": "Error updating diary", "An error occurred while exporting the diary": "An error occurred while exporting the diary", "An error occurred while deleting the diary": "An error occurred while deleting the diary", "An error occurred while clipping the fields": "An error occurred while clipping the fields", "The diary has been deleted successfully": "The diary has been deleted successfully", "Selected layer does’t contain valid data for EKATTE": "Selected layer does’t contain valid data for EKATTE", "Selected layer does’t contain valid data for crops": "Selected layer does’t contain valid data for crops", "Service temporarily unavailable. Please retry shortly. Thank you for your patience.": "Service temporarily unavailable. Please retry shortly. Thank you for your patience.", "Cannot create diary from an empty layer": "Cannot create diary from an empty layer", "Search by name, phase, crop or pest": "Search by name, phase, crop or pest", "Avg dose per dka": "Avg dose per dka", "Total quantity used": "Total quantity used", "Please select plots with the same crop to enter variety/hybrid changes. For convenience you can use the filter by crop above the table.": "Please select plots with the same crop to enter variety/hybrid changes. For convenience you can use the filter by crop above the table.", "There are plots in the selection for which a Variety/Hybrid has already been introduced. If you do not want them to be updated, exclude them from the selection using the Variety/Hybrid filter.": "There are plots in the selection for which a Variety/Hybrid has already been introduced. If you do not want them to be updated, exclude them from the selection using the Variety/Hybrid filter.", "There are plots in the selection for which an ancestor has already been introduced. If you do not want them to be updated, exclude them from the selection using the Predecessor filter.": "There are plots in the selection for which an ancestor has already been introduced. If you do not want them to be updated, exclude them from the selection using the Predecessor filter.", "There are plots in the selection for which there is already a plant stock in place.": "There are plots in the selection for which there is already a plant stock in place.", "There are plots in the selection for which a predecessor has already been introduced. If you do not want them to be updated, exclude them from the selection using the predecessor filter.": "There are plots in the selection for which a predecessor has already been introduced. If you do not want them to be updated, exclude them from the selection using the predecessor filter.", "Create new": "Create new", "Add to fields": "Add to fields", "Herbicide": "Herbicide", "Fungicide": "Fungicide", "Insecticide": "Insecticide", "GrowthRegulator": "GrowthRegulator", "Repellent": "Repellent", "Rodenticide": "Rodenticide", "Pheromone": "Pheromone", "Pheromone dispenser": "Pheromone dispenser", "Desiccant/Defoliant": "Desiccant/Defoliant", "Nematicide": "Nematicide", "Molluscicide": "Molluscicide", "Acaricide": "Acaricide", "Limacide": "<PERSON><PERSON><PERSON>", "Elicitor of natural defense mechanisms for crops": "Elicitor of natural defense mechanisms for crops", "Ground": "Ground", "FirstProfessional": "First professional", "SecondProfessional": "Second professional", "NonProfessional": "Non professional", "Failed to add diary events": "Failed to add diary events", "The events have been added successfully": "The events have been added successfully", "An error occurred while loading the units of measure": "An error occurred while loading the units of measure", "Please select only fields that have the same crop in order to add events": "Please select only fields that have the same crop in order to add events", "You have no prescriptions available that are applicable to the selected crop": "You have no prescriptions available that are applicable to the selected crop", "Restriction period if plot is in NUZ": "Restriction period if plot is in NUZ", "Restriction period if plot is NOT in NUZ": "Restriction period if plot is NOT in NUZ", "Rules for products with Nitrogen (N)": "Rules for products with Nitrogen (N)", "Will be added as an event in selected plots": "Will be added as an event in selected plots", "Operation dates": "Operation dates", "Select different start and end date to distribute operations for the range, so the application is not in the same date for all fields. If you are adding operation for only one field, select same start and end date.": "Select different start and end date to distribute operations for the range, so the application is not in the same date for all fields. If you are adding operation for only one field, select same start and end date.", "The event cannot be added": "The event cannot be added", "The fields have been updated successfully": "The fields have been updated successfully", "yes": "yes", "no": "no", "If you enter a value in the Maximum Nitrogen field, the system will calculate the cumulative amount of all nitrogen events and products entered and will not allow you to exceed it.": "If you enter a value in the Maximum Nitrogen field, the system will calculate the cumulative amount of all nitrogen events and products entered and will not allow you to exceed it.", "If you enter a value in the Maximum nitrogen per scheme field, the system will calculate the cumulative amount of all entered nitrogen events and products only for the plots of the selected scheme and will not allow you to exceed it.": "If you enter a value in the Maximum nitrogen per scheme field, the system will calculate the cumulative amount of all entered nitrogen events and products only for the plots of the selected scheme and will not allow you to exceed it.", "If you enter periods with restrictions on the use of the product in and out of NVZs (Nitrate Vulnerable Zones), the system will not allow you to enter events with that product if the date of the event falls within the relevant restriction period.": "If you enter periods with restrictions on the use of the product in and out of NVZs (Nitrate Vulnerable Zones), the system will not allow you to enter events with that product if the date of the event falls within the relevant restriction period.", "The maximum number of applications for product has been reached.": "The maximum number of applications for product has been reached.", "The maximum amount of nitrogen per dka has been reached for plot.": "The maximum amount of nitrogen per dka has been reached for plot.", "The maximum amount of nitrogen per dka for scheme has been reached for plot.": "The maximum amount of nitrogen per dka for scheme has been reached for plot.", "One of the products you are trying to apply has a restriction period in the nuz.": "One of the products you are trying to apply has a restriction period in the nuz.", "One of the products you are trying to apply has a restriction to participate in the scheme.": "One of the products you are trying to apply has a restriction to participate in the scheme.", "Sorry, there is a restriction on the time gap between applications, preventing one or more of the products from being applied.": "Sorry, there is a restriction on the time gap between applications, preventing one or more of the products from being applied.", "Selected prescriptions": "Selected prescriptions", "Farm information": "Farm information", "Select application": "Select application", "Error loading diary data": "Error loading diary data", "Error loading fields": "Error loading fields", "This operation could take a while": "This operation could take a while", "Intersect with KVS": "Intersect with KVS", "An error occured while exporting the intersecting the active layer with KVS": "An error occured while exporting the intersecting the active layer with KVS", "Failed to load KVS list": "Failed to load KVS list", "Failed to load package data": "Failed to load package data", "Manage KVS": "Manage KVS", "Received": "Received", "Received in lab date": "Received in lab date", "Requested": "Requested", "For sync": "For sync", "Processing": "Processing", "Successfully processed": "Successfully processed", "Invalid Shape file": "Invalid Shape file", "Invalid DBF file": "Invalid DBF file", "Invalid archive": "Invalid archive", "Invalid polygon geometry": "Invalid polygon geometry", "Invalid ISAK file": "Invalid ISAK file", "System error during processing": "System error during processing", "Invalid attribute information": "Invalid attribute information", "Intersection with existing objects": "Intersection with existing objects", "In progress": "In progress", "Waiting for definition": "Waiting for definition", "Copying data": "Copying data", "Invalid projection": "Invalid projection", "Not allowed to add new data": "Not allowed to add new data", "Encoding problem": "Encoding problem", "Encoding problem in the column name of the table": "Encoding problem in the column name of the table", "Missing column": "Missing column", "Partially processed": "Partially processed", "Inconsistent file type": "Inconsistent file type", "Processing started...": "Processing started...", "Error reading shape object": "Error reading shape object", "Error: Geometry Collection present!": "Error: Geometry Collection present!", "Contracts not updated": "Contracts not updated", "Adding KVS in process...": "Adding KVS in process...", "Loaded": "Loaded", "Available": "Available", "Available for sublease for": "Available for sublease for", "Available number of localities according to your plan": "Available number of localities according to your plan", "Sync KVS automatically": "Sync KVS automatically", "Added only": "Added only", "Not added": "Not added", "Filter by": "Filter by", "Search by name or EKATTE": "Search by name or EKATTE", "KVS name": "KVS name", "Date of last sync": "Date of last sync", "Date of last synchronization": "Date of last synchronization", "Date of event": "Date of event", "Adding": "Adding", "Syncing": "Syncing", "Failed to request KVS": "Failed to request KVS", "There are no intersecting plots with KVS": "There are no intersecting plots with KVS", "The layer has been deleted successfully": "The layer has been deleted successfully", "Add KVS": "Add KVS", "Confirm adding KVS": "Please confirm adding <b>({{ekatteCode}}) {{ekatteName}}</b> to your KVS.", "Once started the process is irreversible.": "Once started the process is irreversible.", "Added": "Added", "Updating": "Updating", "An error occurred while updating multiple fields in the layer.": "An error occurred while updating multiple fields in the layer.", "Data will be saved for all filtered plots. If no filters are applied, all plots will be reflected for the selected business year. If there are unfilled columns, they will remain unchanged.": "Data will be saved for all filtered plots. If no filters are applied, all plots will be reflected for the selected business year. If there are unfilled columns, they will remain unchanged.", "T": "yes", "F": "no", "Use only geometries which are from the selected types": "Use only geometries which are from the selected types", "Not updated contracts": "Not updated contracts", "Create filter": "Create filter", "New filter": "New filter", "Archived fields included": "Archived fields included", "Loaded files": "Loaded files", "Field status": "Field status", "Layer settings": "Layer settings", "Type of labels": "Type of labels", "Style": "Style", "Sinle color": "Sinle color", "By column": "By column", "Choose this option to color the map by value": "Choose this option to color the map by value", "Column for borders": "Column for borders", "Column to fill": "Column to fill", "Masiv": "Masiv", "block": "Block", "Allowable type": "Allowable type", "Not selected": "Not selected", "column warning": "The column {{ column }} contains more than 20 unique values. Visualization may be compromised.", "column error": "The column {{ column }} contains more than 100 unique values. Visualization may be compromised.", "column no values": "The column {{column}} does not contain any values, so it cannot be used for coloring.", "When selecting by column it is required to select at least one column": "When selecting by column it is required to select at least one column", "Styles updated successfully": "Styles updated successfully", "Fill color": "Fill color", "Transparency for fill": "Transparency for fill", "Borders color": "Borders color", "Border width": "Border width", "Seller": "<PERSON><PERSON>", "Buyer": "Buyer", "Lessee": "Lessee", "Landlord": "Landlord", "Tenant": "Tenant", "Party A": "Party A", "Party B": "Party B", "Processor": "Processor", "Customer": "Customer", "Lease": "Lease", "Rent": "Rent", "Agreement": "Agreement", "JointProcessing": "Joint processing", "Sale": "Sale", "Subrent": "Subrent", "Sublease": "Sublease", "sublease": "sublease", "Sublease area": "Sublease area", "Error loading column values": "Error loading column values", "Show all fillings": "Show all fillings", "Hide all fillings": "Hide all fillings", "Single color": "Single color", "This option is not available for large number of plots": "This option is not available for large number of plots", "Information from OSZ": "Information from OSZ", "Attached Files": "Attached Files", "In cash": "In cash", "In kind": "In kind", "Аnnuity": "Аnnuity", "Lock document": "Lock document", "Unlock document": "Unlock document", "Rent area": "Rent area", "Allowable area": "Allowable area", "Rent per plot": "Rent per plot", "Show owners tree": "Show owners tree", "Total plots": "Total plots", "by contract": "by contract", "for rent": "for rent", "allowable": "allowable", "by document": "by document", "The owner is dead and does not have any heritors": "The owner is dead and does not have any heritors", "Less than 100% ownership is set. The rent is calculated based on the entered information for ownership and contract area.": "Less than 100% ownership is set. The rent is calculated based on the entered information for ownership and contract area.", "Contract effective from": "Contract effective from", "Contract signed": "Contract signed", "Contract not signed": "Contract not signed", "Successor of": "Successor of", "Family tree": "Family tree", "Power of attorney data": "Power of attorney data", "Power of attorney": "Power of attorney", "Notary public": "Notary public", "The owner is deceased and no successors added.": "The owner is deceased and no successors added.", "Search by identificator": "Search by identiicator", "Owners": "Owners", "Without rent in kind": "Without rent in kind", "Notarial deed": "Notarial deed", "Tom": "<PERSON>", "Delo": "Delo", "Notarial number": "Notarial number", "Court": "Court", "OSZ": "OSZ", "No features selected": "No features selected", "Land properties": "Land properties", "Failed to download document": "Failed to download document", "lv": "lv.", "No owners found": "No owners found", "Subleases": "Subleases", "Annuled": "Annuled", "Multiedit": "Multiedit", "Area by contract": "Area by contract", "No contracts": "No contracts", "No contracts for renewal": "No contracts for renewal", "Currently, there are no contracts requiring renewal": "Currently, there are no contracts requiring renewal.", "Total area by document": "Total area by document", "Current year area": "Current year area", "Next year area": "Next year area", "Renewable contracts": "Renewable contracts", "Renewed contracts": "Renewed contracts", "Auto renewal contracts": "Auto renewal contracts", "Total contracts": "Total contracts", "No documents": "No documents", "There are no owners entered in the contract, please correct it!": "There are no owners entered in the contract, please correct it!", "There are no owners and properties entered in the contract, please correct it!": "There are no owners and properties entered in the contract, please correct it!", "Buyers": "Buyers", "Inheritants of": "Inheritants of", "No buyers found": "No buyers found", "Sales area": "Sales area (dka)", "Price/dka": "Price/dka (lv)", "Amount": "Amount (lv)", "No results found": "No results found", "Contacts": "Contacts", "Active from": "Active from", "Add file": "Add file", "Annexed": "Annexed", "Annex": "Annex", "Annul": "<PERSON><PERSON>", "No annexes found": "No annexes found", "Expired(single)": "Expired", "Information from the Registration Service": "Information from the Registration Service", "In cash-fixed": "In cash-fixed", "Annexes": "Annexes", "Registration Service": "Registration Service", "Failed to load documents list": "Failed to load documents list", "Officially created contract": "Officially created contract", "No filter": "No filter", "Closed for editing": "Closed for editing", "Notarial act number": "Notarial act number", "Incomplete ownership details": "Incomplete ownership details", "Maturity date": "Maturity date", "Rent type": "Rent type", "Kind type": "Kind type", "Contragent type": "Contragent type", "Contract signer": "Contract signer", "Group": "Group", "Specific rent type": "Specific rent type", "Contragent": "Contragent", "Heritor": "<PERSON><PERSON>", "Financial result": "Financial result", "Sold": "Sold", "Sold area": "Sold area", "Mortgage": "Mortgage", "Mortgage area": "Mortgage area", "The total area is calculated by selecting one farming year and one farm": "The total area is calculated by selecting one farming year and one farm.", "EPORD": "EPORD", "Integration with EPORD has not yet been made": "Integration with EPORD has not yet been made", "Login in EPORD": "Login in EPORD", "To start the process, you need to log in to EPORD": "To start the process, you need to log in to EPORD.", "Please input your E-mail": "Please input your E-mail!", "Please input your Password": "Please input your Password!", "Log in": "Log in", "Failed entry into EPORD. Try again": "Failed entry into EPORD. Try again.", "You are not logged into EPORD. To add a Prescription, please": "You are not logged into EPORD. To add a Prescription, please", "login to your account": "login to your account", "The prescription will be added to the EPORD by email": "The prescription will be added to the EPORD by {{email}}.", "If you wish to change the profile": "If you wish to change the profile", "please select another profile": "please select another profile", "You have selected a product that is missing from EPORD, so the event will only be added to the Diary": "You have selected a product that is missing from EPORD, so the event will only be added to the Diary.", "Invalid period: event are added ≥3 days in advance and with a  minimum duration of 1 hour": "Invalid period: event are added ≥3 days in advance and with a  minimum duration of 1 hour.", "Delete all events in this block": "Delete all events in this block", "Add for selected plots in Diary": "Add for selected plots in Diary", "This is a list of events that have been entered into the EPORD but not entered into the Diary. To add them, select an event from EPORD and press the arrow.": "This is a list of events that have been entered into the EPORD but not entered into the Diary. To add them, select an event from EPORD and press the arrow.", "Products marked in orange do not have a prescription in place.": "Products marked in orange do not have a prescription in place.", "Add prescription to diary": "Add prescription to diary", "Add to diary": "Add to diary", "If you click the 'Delete' button, you will remove the prescription from the current diary": "If you click the 'Delete' button, you will remove the prescription from the current diary.", "To delete the prescription from all diaries, you need to check the 'Delete from all diaries' option": "To delete the prescription from all diaries, you need to check the 'Delete from all diaries' option.", "Events entered in the EPORD will be cancelled if they are not active or have already completed": "Events entered in the EPORD will be cancelled if they are not active or have already completed.", "You are editing a prescription included in diaries. To edit it everywhere, check 'Edit in all diaries' at the end of the form": "You are editing a prescription included in {{diariesCount}} diaries. To edit it everywhere, check 'Edit in all diaries' at the end of the form.", "The prescription will be edited for this diary. Changes made will not be reflected in the other diaries": "The prescription will be edited for this diary. Changes made will not be reflected in the other diaries.", "The events introduced in the EPORD will not be cancelled": "The events introduced in the EPORD will not be cancelled.", "Event entered in the EPORD will be cancelled if they are not active or have already completed": "Event entered in the EPORD will be cancelled if they are not active or have already completed.", "Event entered in the EPORD will be cancelled": "Event entered in the EPORD will be cancelled.", "The selected user does not match the user associated with this diary.": "The selected user does not match the user associated with this diary.", "The Epord user is already assigned to a different organization.": "The Epord user is already assigned to a different organization.", "There is a possibility of EPORD integration": "There is a possibility of EPORD integration", "There is no possibility for EPORD integration": "There is no possibility for EPORD integration", "The event cannot be deleted, because it is in a period of execution": "The event cannot be deleted, because it is in a period of execution.", "The event cannot be deleted as it has already been completed": "The event cannot be deleted as it has already been completed.", "Cannot load the report data for the selected period. Please, try for a shorter period.": "Cannot load the report data for the selected period. Please, try for a shorter period.", "Contract renewal": "Contract renewal", "Show plots": "Show plots", "With automatic renewal": "With automatic renewal", "Without automatic renewal": "Without automatic renewal", "This page shows the properties for which the leased/rented area in the current year is less than that in the following year": "This page shows the properties for which the leased/rented area in the current year is less than that in the following year.", "Contract renewal data": "Contract renewal data", "Select all renewals": "Select all renewals", "Renewal": "Renewal", "Multi-year contract - manual renewal required": "Multi-year contract - manual renewal required", "Only  1 year  contracts can be renewed automatically": "Only  1 year  contracts can be renewed automatically", "Contract period does not match the selected farming year": "Contract period does not match the selected farming year", "The renewal of this contract will cause an increase in the total contract area for the next farming year compared to the current one!": "The renewal of this contract will cause an increase in the total contract area for the next farming year compared to the current one!", "Cannot renew the contract, because the document area will exceed": "Cannot renew the contract, because the document area will exceed", "Contracts area(dka)": "Contracts area(dka)", "Total contract area": "Total contract area"}