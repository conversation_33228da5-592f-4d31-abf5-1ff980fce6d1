import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { inject, Injectable } from "@angular/core";
import {
    FilterSearchEnum,
    IndexChartEnum,
    PlotAvgIndexRangeType,
    RequestParamsEnum,
    RGBType,
} from "@geoscan/main-lib/types";
import { isArray, isEmpty, isNil } from "lodash";
import moment from "moment";
import Feature from "ol/Feature";
import { Observable } from "rxjs";

@Injectable({
    providedIn: "root",
})
export class HelperService {
    constructor() {}

    private http = inject(HttpClient);

    downloadFile(blob: Blob, filename: string): void {
        const objectURL = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = objectURL;
        link.download = filename;
        link.target = "_blank";
        document.body.appendChild(link);
        link.click();
        link.remove();
        URL.revokeObjectURL(objectURL);
    }

    downloadFileFromUrl(url: string): Observable<Blob> {
        const headers = new HttpHeaders({
            "Content-Type": "application/json",
        });

        return this.http.get(url, {
            headers,
            responseType: "blob",
        });
    }

    createHttpParams(
        urlParam: Array<
            [FilterSearchEnum | RequestParamsEnum, string | number | boolean]
        >
    ) {
        let params = new HttpParams();

        urlParam.forEach((data) => {
            const [key, value] = data;

            params = params.append(key, value);
        });

        return params;
    }

    formatIntNumber(value: number): string {
        const formattedValue = value
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, " ");
        return formattedValue;
    }

    filterFeaturesByPropertyValue(
        listFeatures: Feature[],
        arrFilters: (string | number)[],
        name: string,
        subPropertyName: string = ""
    ) {
        return listFeatures.filter((platform: Feature) => {
            const platformProperties = platform.getProperties();

            if (subPropertyName.length > 0) {
                return (
                    platformProperties[subPropertyName] &&
                    arrFilters.includes(
                        platformProperties[subPropertyName][name]
                    )
                );
            } else {
                const value = platformProperties[name];
                return arrFilters.includes(value);
            }
        });
    }

    filterFeaturesByPropertyValues(
        listFeatures: Feature[],
        arrFilters: (string | number)[],
        name: string
    ) {
        return listFeatures.filter((platform: Feature) => {
            const platformProperties = platform.getProperties();
            return arrFilters.some((value: number | string) => {
                return (
                    platformProperties[name] &&
                    platformProperties[name].includes(value)
                );
            });
        });
    }

    getFarmYearStart(farmYear: number) {
        return `${farmYear - 1}-10-01 00:00`;
    }

    getFarmYearEnd(farmYear: number) {
        return `${farmYear}-09-30 23:59`;
    }

    getFarmYearByDate(date: string | Date): number {
        let farmYear = moment(date).year();
        const month = moment(date).month() + 1;

        if (month > 9) {
            farmYear = farmYear + 1;
        }

        return farmYear;
    }

    getCurrentFarmYearText() {
        const farmYear = this.getFarmYearByDate(new Date());
        return `${farmYear - 1}/${farmYear}`;
    }

    getAvgIndexRange(avgIndex: number): PlotAvgIndexRangeType {
        const lastDigit = avgIndex % 10;

        let lowerBoundary = avgIndex <= 0 ? 0 : 90;
        let upperBoundary = avgIndex <= 0 ? 10 : 100;

        if (avgIndex > 0 && avgIndex < 100) {
            upperBoundary =
                lastDigit > 0 ? avgIndex + (10 - lastDigit) : avgIndex + 10;

            lowerBoundary = lastDigit > 0 ? avgIndex - lastDigit : avgIndex;
        }

        return {
            lowerBoundary,
            upperBoundary,
        };
    }

    // TODO:: GPS-1724 Check where is used in html like:"helperService.getAvgIndexColorClass()"
    getAvgIndexColorClass(
        avgIndex: number | string,
        indexType?: IndexChartEnum
    ) {
        const avgIndexNum = Number(avgIndex);
        let classPrefix = "index-color-";

        if (!avgIndexNum) {
            return "";
        }

        if (indexType) {
            classPrefix =
                indexType === IndexChartEnum.Vegetation
                    ? "index-color-"
                    : "moisture-index-color-";
        }

        const avgIndexRange = this.getAvgIndexRange(avgIndexNum);

        return (
            classPrefix +
            avgIndexRange.lowerBoundary +
            "-" +
            avgIndexRange.upperBoundary
        );
    }

    hex2Rgb(colorHex: string): RGBType {
        const [, red, green, blue] =
            /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(colorHex);

        return {
            red: parseInt(red, 16),
            green: parseInt(green, 16),
            blue: parseInt(blue, 16),
        } as RGBType;
    }

    hex2RgbArray(colorHex: string): number[] {
        return Object.values(this.hex2Rgb(colorHex));
    }

    hex2RgbStr(colorHex: string): string {
        const colorRGB = this.hex2Rgb(colorHex);
        return colorRGB.red + " " + colorRGB.green + " " + colorRGB.blue;
    }

    colorIsLight(r: number, g: number, b: number) {
        // Counting the perceptive luminance
        // human eye favors green color...
        // See http://stackoverflow.com/a/1855903/186965
        const a = 1 - (0.299 * r + 0.587 * g + 0.114 * b) / 255;
        return a < 0.5;
    }

    calculatePercentageFromTotal(total: number, currentValue: number) {
        const percentage = (currentValue / total) * 100;

        return percentage.toFixed(2);
    }

    isHexColor(str: string, includesAlphaChannel: boolean = false): boolean {
        return includesAlphaChannel
            ? /^#[0-9A-F]{6,8}$/i.test(str)
            : /^#[0-9A-F]{6}$/i.test(str);
    }

    generateRandomHexColor(withHash: boolean = false): string {
        const randomColor = Math.floor(Math.random() * 16777215).toString(16); // Generate random number between 0 and 16777215 (0xFFFFFF in hexadecimal)
        const prefix = withHash ? "#" : "";
        return prefix + randomColor.padStart(6, "0"); // Ensure the color code is 6 characters long and add a "#" prefix
    }

    timestampToDateRange(timestamp: string[]) {
        const from = moment.unix(parseInt(timestamp[0])).toDate();
        const to = moment.unix(parseInt(timestamp[1])).toDate();

        return [from, to];
    }

    isValidData(value: unknown) {
        return (
            (!isArray(value) && !isNil(value) && value !== "") ||
            (isArray(value) && !isEmpty(value))
        );
    }
}
