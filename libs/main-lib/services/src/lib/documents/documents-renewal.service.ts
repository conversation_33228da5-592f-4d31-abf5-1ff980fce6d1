import { HttpClient } from "@angular/common/http";
import { Injectable, inject } from "@angular/core";
import {
    AdministrativeMapFilterItemType,
    BaseResponseType,
    ContractRenewalStructureType,
    DocumentTypeEnum,
    FilterSearchEnum,
    LabelValue,
    PagingOptionsMultipleSortType,
    RequestParamsEnum,
} from "@geoscan/main-lib/types";
import { Observable } from "rxjs";

import { HelperService } from "../helpers/helper.service";
import { SERVICES_CONFIG_TOKEN } from "../services-config.token";

@Injectable({
    providedIn: "root",
})
export class DocumentsRenewalService {
    private http = inject(HttpClient);
    private config = inject(SERVICES_CONFIG_TOKEN);
    private helperService = inject(HelperService);
    private readonly baseUrl = `${this.config.technofarmLaravelApiUrl}/api/documents`;

    getFilterItems(
        documentType: DocumentTypeEnum.Contracts | DocumentTypeEnum.Subleases,
        colName: string,
        filterParams: AdministrativeMapFilterItemType,
        pagingOptions: Partial<PagingOptionsMultipleSortType>,
        search?: string
    ): Observable<BaseResponseType<LabelValue<string>>> {
        const params: Array<
            [FilterSearchEnum | RequestParamsEnum, string | number | boolean]
        > = [
            [RequestParamsEnum.ColName, colName],
            [RequestParamsEnum.FilterParams, JSON.stringify(filterParams)],
            [RequestParamsEnum.Page, pagingOptions.pageIndex],
            [RequestParamsEnum.Limit, pagingOptions.pageSize],
        ];

        if (search) {
            params.push([RequestParamsEnum.Search, search]);
        }

        return this.http.get<BaseResponseType<LabelValue<string>>>(
            `${this.baseUrl}/${documentType}/renew/filter-items`,
            { params: this.helperService.createHttpParams(params) }
        );
    }

    getDocumentsForRenewal(
        documentType: DocumentTypeEnum.Contracts | DocumentTypeEnum.Subleases,
        filterParams: AdministrativeMapFilterItemType,
        pagingOptions: Partial<PagingOptionsMultipleSortType>
    ): Observable<ContractRenewalStructureType> {
        // Input validation
        if (!filterParams) {
            throw new Error("Filter parameters are required");
        }

        if (!pagingOptions.pageIndex || !pagingOptions.pageSize) {
            throw new Error("Page index and page size are required");
        }

        const enhancedFilterParams = {
            ...filterParams,
            has_decreased_area_next_year: true,
        };

        const params: Array<
            [FilterSearchEnum | RequestParamsEnum, string | number | boolean]
        > = [
            [
                RequestParamsEnum.FilterParams,
                JSON.stringify(enhancedFilterParams),
            ],
            [RequestParamsEnum.Page, pagingOptions.pageIndex],
            [RequestParamsEnum.Limit, pagingOptions.pageSize],
        ];

        return this.http.get<ContractRenewalStructureType>(
            `${this.baseUrl}/${documentType}/renew/`,
            { params: this.helperService.createHttpParams(params) }
        );
    }
}
