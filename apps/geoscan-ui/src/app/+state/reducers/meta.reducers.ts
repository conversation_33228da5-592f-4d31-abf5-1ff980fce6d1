import { Action, ActionReducer, MetaReducer, State } from "@ngrx/store";
import { merge } from "lodash";
import { localStorageSync } from "ngrx-store-localstorage";

const INIT_ACTION = "@ngrx/store/init";
const UPDATE_ACTION = "@ngrx/store/update-reducers";

const mergeReducer = (
    state: State<unknown>,
    rehydratedState: State<unknown>,
    action: Action
) => {
    if (
        (action.type === INIT_ACTION || action.type === UPDATE_ACTION) &&
        rehydratedState
    ) {
        state = merge(state, rehydratedState);
    }

    return state;
};

export function localStorageSyncReducer(
    reducer: ActionReducer<State<unknown>>
): ActionReducer<State<unknown>> {
    return localStorageSync({
        keys: [
            {
                map: [
                    { baseLayer: ["name"] },
                    { mapLayers: ["ids", "entities"] },
                ],
            },
            {
                administrativeMap: [
                    "selectedLayers",
                    "selectedActiveLayer",
                    "filters",
                    "attributeInfo.LayersSettings",
                ],
            },
            "filter",
            "documents.Filter",
            "contractsRenewal.Filter",
        ],
        rehydrate: true,
        mergeReducer,
        restoreDates: false,
    })(reducer);
}

export const metaReducers: Array<MetaReducer<State<unknown>, Action>> = [
    localStorageSyncReducer,
];
