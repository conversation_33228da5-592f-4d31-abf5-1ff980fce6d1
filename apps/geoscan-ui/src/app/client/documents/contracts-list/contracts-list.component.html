<gs-documents-horizontal-filter
    [documentType]="documentType"
></gs-documents-horizontal-filter>

<div class="mb-lg">
    <gs-documents-attribute-toolbar
        [totalCount]="totalDocuments$ | async"
        [documentType]="documentType"
    ></gs-documents-attribute-toolbar>
</div>

<gs-documents-table
    [documentType]="documentType"
    [listData]="documentsList$ | async"
    [loading]="documentsListLoading$ | async"
    [footerData]="documentsFooter$ | async"
    [allItemsSelected]="allDocumentsSelected$ | async"
    [selectedItemsIds]="selectedDocumentsIds$ | async"
    [totalItems]="totalDocuments$ | async"
    [pagingOptions]="pagingOptions$ | async"
    [tableWidthConfig]="tableWidthConfig"
    [documentColumns]="documentColumns"
    [documentFooter]="documentFooter"
    (pagingOptionsChange)="onPagingOptionsChange($event)"
    (allItemsSelectionChange)="onAllDocumentsSelectionChange($event)"
    (itemSelectionChange)="onDocumentsSelectionChange($event)"
    (showContractDetails)="onShowContractDetails($event)"
>
    <ng-template #tableBody let-document>
        <td>
            <div class="display-flex flex-align-center gap-md">
                <span [ngClass]="document.details | documentIcon"></span>
                <div>
                    <div
                        class="font-md font-medium l-h-md primary-brand-color-text"
                    >
                        {{ document.details.c_num }}
                    </div>
                    <div
                        class="font-md font-medium l-h-md"
                        [ngClass]="{
                            'text-color-secondary': isExpiredOrCanceled(
                                document.details.status
                            ),
                        }"
                    >
                        {{ document.details.c_date }}
                    </div>
                </div>
            </div>
        </td>
        <td>
            <nz-tag
                nz-col
                nz-tooltip
                [nzTooltipTitle]="document.details.type"
                [nzColor]="document.details.c_type | documentColorByType"
                class="contract-type ml-8"
            >
                {{ document.details.type }}
            </nz-tag>
        </td>
        <td
            [ngClass]="{
                'text-color-secondary': isExpiredOrCanceled(
                    document.details.status
                ),
            }"
        >
            <span
                [ngClass]="{
                    'text-color-secondary': isExpiredOrCanceled(
                        document.details.status
                    ),
                }"
            >
                {{ document.details.start_date }}
            </span>
        </td>
        <td
            [ngClass]="{
                'text-color-secondary': isExpiredOrCanceled(
                    document.details.status
                ),
            }"
        >
            <span
                [ngClass]="{
                    'text-color-secondary': isExpiredOrCanceled(
                        document.details.status
                    ),
                }"
            >
                {{ document.details.due_date }}
            </span>
        </td>
        <td
            [ngClass]="{
                'text-color-secondary': isExpiredOrCanceled(
                    document.details.status
                ),
            }"
        >
            {{ document.details.farming_name }}
        </td>
        <td
            [ngClass]="{
                'text-color-secondary': isExpiredOrCanceled(
                    document.details.status
                ),
            }"
        >
            @if (document.details.ekatte_names?.length > 0) {
                <div class="display-flex flex-align-center">
                    <div class="mr-xs">
                        {{ document.details.ekatte_names[0] }}
                    </div>
                    @if (document.details.ekatte_names.length > 1) {
                        <nz-tag
                            nz-tooltip
                            [nzTooltipTitle]="
                                document.details.ekatte_names
                                    ?.slice(1)
                                    .join(', ')
                            "
                            nzColor="default"
                        >
                            +{{ document.details.ekatte_names.length - 1 }}
                        </nz-tag>
                    }
                </div>
            }
        </td>
        <td
            [ngClass]="{
                'text-color-secondary': isExpiredOrCanceled(
                    document.details.status
                ),
            }"
        >
            {{ document.details.total_contract_area | number: "1.0-3" }}
        </td>
        <td>
            <nz-badge
                nz-col
                class="font-medium l-h-lg font-md"
                [nzColor]="document.details.status | documentStatusColor"
                [nzText]="document.details.status | translate"
                [ngClass]="{
                    'text-color-secondary': isExpiredOrCanceled(
                        document.details.status
                    ),
                }"
            ></nz-badge>
        </td>
        <td
            [ngClass]="{
                'text-color-secondary': isExpiredOrCanceled(
                    document.details.status
                ),
            }"
        >
            {{ document.details.comment }}
        </td>
    </ng-template>
</gs-documents-table>
