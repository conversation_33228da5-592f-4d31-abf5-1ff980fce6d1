import {
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    input,
    output,
    signal,
} from "@angular/core";
import {
    ContractRenewalStructureType,
    DocumentStatusEnum,
} from "@geoscan/main-lib/types";

@Component({
    selector: "gs-contracts-renewal-table",
    templateUrl: "./contracts-renewal-table.component.html",
    styleUrls: ["./contracts-renewal-table.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractsRenewalTableComponent {
    contractsData = input<ContractRenewalStructureType>();
    loading = input<boolean>(false);
    pageIndex = input.required<number>();
    pageSize = input.required<number>();
    pageSizeChange = output<number>();

    rows = computed(() => this.contractsData()?.rows ?? []);
    total = computed(() => this.contractsData()?.total ?? 0);
    footer = computed(() => this.contractsData()?.footer ?? null);
    columns = computed(() => this.contractsData()?.columns ?? []);

    // Table state
    expandSet = signal<Set<number>>(new Set<number>());

    documentStatusEnum = DocumentStatusEnum;

    constructor() {
        effect(
            () => {
                const allRowsPlotIds = new Set<number>(
                    this.rows().map((row) => row.plot_id)
                );
                this.expandSet.set(allRowsPlotIds);
            },
            { allowSignalWrites: true }
        );
    }

    onExpandChange(id: number): void {
        const currentSet = new Set(this.expandSet());
        if (currentSet.has(id)) {
            currentSet.delete(id);
        } else {
            currentSet.add(id);
        }
        this.expandSet.set(currentSet);
    }
}
