@if (rows().length > 0 || loading()) {
    <div [gsAutoHeightDiv]="125">
        <nz-table
            #documentsTable
            nzBordered
            nzShowPagination
            [nzFrontPagination]="false"
            [nzData]="rows()"
            [nzPageSize]="pageSize()"
            [nzTotal]="total()"
            [nzPageIndex]="pageIndex()"
            (nzPageIndexChange)="pageSizeChange.emit($event)"
            [nzLoading]="loading()"
            [gsAutoHeightTable]="60"
            class="client-table contracts-renewal-table"
        >
            <!-- Table Header -->
            <thead>
                @if (columns(); as columns) {
                    <!-- First row: parent columns -->
                    <tr>
                        <th
                            nzLeft
                            nzWidth="40px"
                            [rowSpan]="2"
                            class="br-none"
                        ></th>
                        <!-- Expand column -->
                        @for (column of columns; track column.name || $index) {
                            <th
                                [nzLeft]="column.name === 'kad_ident'"
                                [nzWidth]="column.width"
                                [colspan]="column.colspan ?? 1"
                                [rowspan]="column.rowspan ?? 1"
                                nzEllipsis
                                nz-tooltip
                                [nzTooltipTrigger]="
                                    column.tooltip?.title?.length > 0
                                        ? 'hover'
                                        : null
                                "
                                [nzTooltipTitle]="parentTooltipTpl"
                                [nzTooltipPlacement]="
                                    column.tooltip?.placement ?? 'top'
                                "
                                [class]="{
                                    'font-bold': column.name !== 'kad_ident',
                                    'align-center': column.name === 'kad_ident',
                                }"
                            >
                                @if (column.title && column.title.length > 0) {
                                    @for (
                                        titlePart of column.title;
                                        track titlePart
                                    ) {
                                        {{ titlePart | translate }}
                                    }
                                }

                                <ng-template #parentTooltipTpl>
                                    @if (column.tooltip?.title) {
                                        @for (
                                            tooltipPart of column.tooltip.title;
                                            track tooltipPart
                                        ) {
                                            {{ tooltipPart | translate }}
                                        }
                                    }
                                </ng-template>
                            </th>
                        }
                    </tr>
                    <!-- Second row: child columns -->
                    <tr>
                        @for (column of columns; track column.name || $index) {
                            @if (
                                column.children && column.children.length > 0
                            ) {
                                @for (
                                    child of column.children;
                                    track child.name
                                ) {
                                    <th
                                        nzEllipsis
                                        nz-tooltip
                                        [nzTooltipTrigger]="
                                            child.tooltip?.title?.length > 0
                                                ? 'hover'
                                                : null
                                        "
                                        [nzTooltipTitle]="childTooltipTpl"
                                        [nzTooltipPlacement]="
                                            child.tooltip?.placement ?? 'top'
                                        "
                                        [ngClass]="{
                                            'align-left':
                                                child.name === 'document_area',
                                            'align-right':
                                                child.name === 'contract_area',
                                        }"
                                    >
                                        @for (
                                            titlePart of child.title;
                                            track titlePart
                                        ) {
                                            {{ titlePart | translate }}
                                        }

                                        <ng-template #childTooltipTpl>
                                            @if (child.tooltip?.title) {
                                                @for (
                                                    tooltipPart of child.tooltip
                                                        .title;
                                                    track tooltipPart
                                                ) {
                                                    {{
                                                        tooltipPart | translate
                                                    }}
                                                    @if (!$last) {
                                                        <br />
                                                    }
                                                }
                                            }
                                        </ng-template>
                                    </th>
                                }
                            }
                        }
                    </tr>
                }
            </thead>

            <!-- Table Body -->
            <tbody>
                @for (row of rows(); track row.plot_id) {
                    <!-- Main plot row -->
                    <tr
                        [ngClass]="{
                            'expanded-row': expandSet().has(row.plot_id),
                        }"
                        class="l-h-md"
                    >
                        <!-- Expand column -->
                        <td
                            nzLeft
                            [nzExpand]="expandSet().has(row.plot_id)"
                            [nzExpandIcon]="expandIconTpl"
                            [nzShowExpand]="row.contracts_json?.length > 0"
                        ></td>

                        <!-- Kad Ident -->
                        <td nzLeft class="identifier">
                            {{ row.kad_ident }}
                        </td>

                        <!-- Base Period Document Area -->
                        <td class="align-left">
                            {{ row.document_area | number: "1.3" }}
                        </td>

                        <!-- Base Period Contract Area -->
                        <td class="align-right font-bold border-right">
                            {{ row.contract_area | number: "1.3" }}
                        </td>

                        <!-- Next Period Document Area -->
                        <td class="align-left">
                            {{ row.document_area | number: "1.3" }}
                        </td>

                        <!-- Next Period Contract Area -->
                        <td class="align-right font-bold">
                            {{ row.contract_area_next_period | number: "1.3" }}
                        </td>
                    </tr>

                    <!-- Contract rows -->

                    @if (expandSet().has(row.plot_id)) {
                        @for (
                            contract of row.contracts_json;
                            track contract.contract_id
                        ) {
                            <tr
                                [class]="{
                                    'contract-row-error':
                                        contract.contract_id &&
                                        !contract.can_renew,
                                    'contract-row-warning':
                                        contract.contract_id &&
                                        contract.can_renew,
                                }"
                                class="l-h-md"
                            >
                                <!-- Empty expand column -->
                                <td
                                    nzLeft
                                    [nzChecked]="false"
                                    [nzDisabled]="!contract.can_renew"
                                    class="align-left"
                                ></td>
                                <td nzLeft class="align-right">
                                    @if (
                                        contract.can_renew ||
                                        contract.multi_year_contract ||
                                        contract.wrong_contract_period
                                    ) {
                                        <div clas="display-block align-right">
                                            <span
                                                nz-icon
                                                nzType="info-circle"
                                                nzTheme="outline"
                                                class="info-icon"
                                                nz-tooltip
                                                [nzTooltipTitle]="infoTooltip"
                                            >
                                            </span>
                                        </div>

                                        <ng-template #infoTooltip>
                                            @if (contract.can_renew) {
                                                {{
                                                    "The renewal of this contract will cause an increase in the total contract area for the next farming year compared to the current one!"
                                                        | translate
                                                }}
                                            } @else {
                                                @if (
                                                    contract.multi_year_contract
                                                ) {
                                                    {{
                                                        "Only  1 year  contracts can be renewed automatically"
                                                            | translate
                                                    }}
                                                } @else if (
                                                    contract.wrong_contract_period
                                                ) {
                                                    {{
                                                        "Contract period does not match the selected farming year"
                                                            | translate
                                                    }}
                                                }
                                            }
                                        </ng-template>
                                    }
                                </td>
                                <td colSpan="2" class="border-right">
                                    @if (contract.contract_id) {
                                        <gs-contract-renewal-item
                                            [contract]="contract"
                                        ></gs-contract-renewal-item>
                                    }
                                </td>
                                <td colSpan="2">
                                    @if (contract.multi_year_contract) {
                                        {{
                                            "Multi-year contract - manual renewal required"
                                                | translate
                                        }}
                                    } @else if (contract.not_enought_area) {
                                        <div nz-row nzJustify="space-between">
                                            <div nz-col nzFlex="auto">
                                                {{
                                                    "Cannot renew the contract, because the document area will exceed"
                                                        | translate
                                                }}
                                            </div>
                                            <div
                                                nz-col
                                                class="font-bold area-exceed"
                                            >
                                                +{{
                                                    contract.not_enought_area
                                                        | number: "1.3"
                                                        | areaUnitLabel
                                                }}
                                            </div>
                                        </div>
                                    } @else if (
                                        contract.existing?.contract_id
                                    ) {
                                        <gs-contract-renewal-item
                                            [contract]="contract.existing"
                                        ></gs-contract-renewal-item>
                                    }
                                </td>
                            </tr>
                        }
                    }

                    <ng-template #expandIconTpl>
                        <span
                            class="gs-icon-arrow"
                            [ngClass]="{
                                expanded: expandSet().has(row.plot_id),
                            }"
                            (click)="onExpandChange(row.plot_id)"
                        ></span>
                    </ng-template>
                }
            </tbody>
        </nz-table>
    </div>
} @else {
    <div class="no-data pt-48">
        <gs-nodata-placeholder
            nzNotFoundImage="/assets/images/documents/no-data-contract-renewal.svg"
            [nzNotFoundContent]="noDataTemplate"
        ></gs-nodata-placeholder>
    </div>

    <ng-template #noDataTemplate>
        <div class="no-data-content">
            <div class="text-color-primary font-xxl font-bold my-lg">
                {{ "No contracts for renewal" | translate }}
            </div>
            <div class="text-color-secondary font-lg">
                {{
                    "Currently, there are no contracts requiring renewal"
                        | translate
                }}
            </div>
        </div>
    </ng-template>
}
