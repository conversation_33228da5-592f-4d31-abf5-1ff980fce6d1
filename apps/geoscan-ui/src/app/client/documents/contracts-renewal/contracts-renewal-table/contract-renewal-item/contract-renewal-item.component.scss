@use "../../../../../../styles/rem-calc";

.gs-icon-annex-filled,
.gs-icon-contract-filled {
    width: rem-calc.rem-calc(24);
    height: rem-calc.rem-calc(24);
}

.gs-icon-contract-filled {
    background-color: var(--icon-brand-color-primary);
}

.gs-icon-annex-filled {
    background-color: var(--icon-purple-color);
}

.gs-icon-subleased-filled {
    background-color: var(--icon-brand-color-secondary);
}

.gs-icon-contract-filled-from-sublease {
    background-color: var(--icon-brand-color-secondary);
}

.contract {
    &-row {
        margin: 0 !important;

        [nz-col] {
            flex: 1;
            &:first-child {
                padding-left: 0 !important;
            }
            &:last-child {
                padding-right: 0 !important;
            }
        }

        .contract-link-btn {
            background-color: transparent;
            height: auto;
        }

        .contract-icon {
            min-width: rem-calc.rem-calc(24);
        }

        .mnw-15 {
            min-width: 15%;
        }

        .mnw-20 {
            min-width: 20%;
        }

        .mnw-35 {
            min-width: 35%;
        }
    }

    &-type {
        max-width: 100%;
        border-radius: 4px;
        text-overflow: ellipsis;
        overflow: hidden;
        vertical-align: middle;
    }
}
