import {
    ChangeDetectionStrategy,
    Component,
    computed,
    inject,
    input,
} from "@angular/core";
import { toSignal } from "@angular/core/rxjs-interop";
import {
    ContractColorByType,
    ContractRenewalContractType,
    DocumentContractTypesMap,
    DocumentTypeEnum,
} from "@geoscan/main-lib/types";
import { Store } from "@ngrx/store";
import { ContractApiActions } from "../../../contract/+state/actions";
import { ContractSelectors } from "../../../contract/+state/selectors";

@Component({
    selector: "gs-contract-renewal-item",
    templateUrl: "./contract-renewal-item.component.html",
    styleUrls: ["./contract-renewal-item.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractRenewalItemComponent {
    contract = input.required<ContractRenewalContractType>();

    contractColorByType = ContractColorByType;

    contractLoading = computed(
        () =>
            this.contractDetailsLoading() &&
            this.contractDetailsId() === this.contract()?.contract_id
    );

    private store = inject(Store);
    private contractDetailsLoading = toSignal(
        this.store.select(ContractSelectors.selectLoading),
        { initialValue: false }
    );
    private contractDetailsId = toSignal(
        this.store.select(ContractSelectors.selectSelectedDocumentId),
        { initialValue: null }
    );

    openContractDetails() {
        const contract = this.contract();
        if (!contract?.contract_id) {
            console.error("Contract ID is missing");
            return;
        }

        const contractType =
            contract.from_sublease || contract.is_sublease
                ? DocumentTypeEnum.Subleases
                : DocumentContractTypesMap.get(contract.contract_type);

        if (!contractType) {
            console.error("Unable to determine contract type");
            return;
        }

        this.store.dispatch(
            ContractApiActions.loadContract({
                contractId: contract.contract_id,
                isSmallComponent: true,
                contractType,
                showInDrawer: true,
            })
        );
    }
}
