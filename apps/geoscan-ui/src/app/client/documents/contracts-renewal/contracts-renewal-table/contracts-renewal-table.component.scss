@use "../../../../../styles/rem-calc";

.no-data {
    ::ng-deep {
        .ant-empty-image {
            height: auto;
        }
    }
}

.font-bold {
    font-family: var(--font-bold) !important;
}

.contracts-renewal-table {
    ::ng-deep {
        .ant-table-header.nz-table-hide-scrollbar {
            overflow: hidden !important;
            padding-right: rem-calc.rem-calc(10);
        }

        .ant-table-bordered {
            .ant-table-thead > tr > th:first-child {
                border-right: 1px solid var(--table-border-color) !important;
            }
            .ant-table-thead > tr > th:last-child {
                border-right: none !important;
            }

            .ant-table-thead > tr > th.br-none {
                border-right: none !important;
            }
        }

        .ant-table-pagination.ant-pagination {
            margin: rem-calc.rem-calc(16) 0 0 0;
        }

        .area-exceed {
            color: var(--text-error-color);
        }

        .info-icon {
            font-size: var(--font-xl) !important;
            vertical-align: middle !important;
        }

        .ant-table-tbody tr td {
            line-height: var(--line-height-md);

            &.border-right {
                border-right: 1px solid var(--table-border-color) !important;
            }
        }

        .ant-table-tbody tr.contract-row {
            // Contract row styling based on renewal status
            &-error td {
                background: var(--table-row-bg-error) !important;

                &:hover {
                    background: var(--table-row-bg-error) !important;
                }
            }

            &-warning td {
                background: var(--table-row-bg-warning) !important;

                &:hover {
                    background: var(--table-row-bg-warning) !important;
                }
            }
        }

        // Expand icon styling
        .gs-icon-arrow {
            width: 18px;
            height: 18px;
            cursor: pointer;
            transition: transform 0.2s ease;
            vertical-align: middle;

            &.expanded {
                transform: rotate(90deg);
            }
        }

        .identifier {
            font-family: var(--font-bold);
        }
    }
}
