<div
    nz-row
    nzAlign="middle"
    [nzGutter]="[16, 0]"
    class="text-color-primary font-medium font-md l-h-md w-100 contract-row"
>
    <div nz-col class="mnw-15 display-flex flex-align-center">
        <button
            nz-button
            nzType="link"
            class="p-0 contract-link-btn display-flex flex-align-center w-100"
            (click)="openContractDetails()"
        >
            @if (!contractLoading()) {
                <span
                    [ngClass]="contract() | documentIcon"
                    class="contract-icon mr-xs"
                ></span>
            } @else {
                <span
                    nz-icon
                    nzType="loading"
                    nzTheme="outline"
                    class="mr-xs"
                ></span>
            }

            <span
                nz-typography
                nzEllipsis
                nz-tooltip
                [nzTooltipTitle]="contract().c_num"
                class="text-color-highlighted-brand-primary"
            >
                {{ contract().c_num }}
            </span>
        </button>
    </div>
    <div
        nz-col
        nz-typography
        nzEllipsis
        class="mnw-20"
        nz-tooltip
        [nzTooltipTitle]="contract().virtual_contract_type"
    >
        <nz-tag
            [nzColor]="contractColorByType[contract().contract_type]"
            class="contract-type font-sm l-h-sm"
        >
            {{ contract().virtual_contract_type }}
        </nz-tag>
    </div>
    <div
        nz-col
        nz-typography
        nzEllipsis
        nz-tooltip
        [nzTooltipTitle]="contract().farming_name"
        class="mnw-15"
    >
        {{ contract().farming_name }}
    </div>
    <div
        nz-col
        nz-typography
        nzEllipsis
        nz-tooltip
        [nzTooltipTitle]="contract().start_date + ' - ' + contract().due_date"
        class="mnw-35"
    >
        {{ contract().start_date }} - {{ contract().due_date }}
    </div>
    <div nz-col class="align-right mnw-15">
        {{ contract().contract_area | number: "1.3" }}
    </div>
</div>
