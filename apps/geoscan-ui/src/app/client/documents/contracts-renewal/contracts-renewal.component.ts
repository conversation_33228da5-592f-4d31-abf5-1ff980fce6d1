import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    inject,
    OnDestroy,
    OnInit,
    signal,
    Signal,
    TemplateRef,
    ViewChild,
} from "@angular/core";
import { takeUntilDestroyed, toSignal } from "@angular/core/rxjs-interop";
import { ActivatedRoute, Router } from "@angular/router";
import {
    DocumentsRenewalService,
    DynamicPageHeaderService,
} from "@geoscan/main-lib/services";
import {
    AdministrativeMapFilterItemType,
    ContractRenewalStructureType,
    DocumentTypeEnum,
    ErrorMessagesEnum,
    RenewalPlotsFilterEnum,
    RenewalPlotsFilterMap,
} from "@geoscan/main-lib/types";
import { Store } from "@ngrx/store";
import { TranslateService } from "@ngx-translate/core";
import { distinctUntilChanged } from "rxjs";
import { ClientUiActions, MessageActions } from "../../../+state/actions";
import { MessageTypeEnum } from "../../../shared/enums";
import { ContractUiActions } from "../contract/+state/actions";
import { ContractsRenewalFilterUiActions } from "./contracts-renewal-filter/+state/actions";
import { ContractsRenewalFilterSelectors } from "./contracts-renewal-filter/+state/selectors";

@Component({
    selector: "gs-contracts-renewal",
    templateUrl: "./contracts-renewal.component.html",
    styleUrls: ["./contracts-renewal.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractsRenewalComponent
    implements OnInit, AfterViewInit, OnDestroy
{
    @ViewChild("sidebarHeader") sidebarHeader: TemplateRef<void>;

    documentTypeEnum = DocumentTypeEnum;

    // Expose enum to template
    readonly renewalPlotsFilterEnum = RenewalPlotsFilterEnum;
    readonly pageSize = 10;

    page = signal<number>(1);
    documentType: DocumentTypeEnum.Contracts | DocumentTypeEnum.Subleases;

    // Segmented options for plots filter
    plotsOptions = [
        { value: RenewalPlotsFilterEnum.All, useTemplate: true },
        {
            value: RenewalPlotsFilterEnum.WithAutomaticRenewal,
            useTemplate: true,
        },
        {
            value: RenewalPlotsFilterEnum.WithoutAutomaticRenewal,
            useTemplate: true,
        },
    ];

    contractsData = signal<ContractRenewalStructureType>({
        rows: [],
        total: 0,
        footer: null,
        columns: [],
    });
    loading = signal<boolean>(false);
    rows = computed(() => this.contractsData()?.rows ?? []);
    total = computed(() => this.contractsData()?.total ?? 0);
    footer = computed(() => this.contractsData()?.footer ?? null);
    noDataOrLoading = computed(() => {
        return this.rows().length === 0 || this.loading();
    });
    selectedPlotsFilter = computed<RenewalPlotsFilterEnum>(() =>
        RenewalPlotsFilterMap.revGet(
            this.filterParams()?.can_renew as boolean | null
        )
    );

    private route = inject(ActivatedRoute);
    private router = inject(Router);
    private pageHeaderService = inject(DynamicPageHeaderService);
    private store = inject(Store);
    private documentsRenewalService = inject(DocumentsRenewalService);
    private destroyRef = inject(DestroyRef);
    private translateService = inject(TranslateService);

    private filterParams: Signal<AdministrativeMapFilterItemType>;

    constructor() {
        // Gather the document type from route data
        this.documentType = this.route.snapshot.data["documentType"];

        // Create signal for filter params based on document type
        this.filterParams = toSignal(
            this.store
                .select(
                    ContractsRenewalFilterSelectors.selectContractsRenewalFilterByType(
                        this.documentType
                    )
                )
                .pipe(distinctUntilChanged()),
            { initialValue: {} }
        );

        this.switchPlotsFilter(
            this.selectedPlotsFilter() ?? RenewalPlotsFilterEnum.All,
            false
        );
    }

    ngOnInit(): void {
        this.initContractDetailsDrawerConfig();

        // Активираме fullscreen режим
        this.store.dispatch(
            ClientUiActions.toggleFullscreenLayout({ isFullscreen: true })
        );
    }

    ngAfterViewInit(): void {
        this.pageHeaderService.setSidebarHeaderTemplate(this.sidebarHeader);
    }

    ngOnDestroy(): void {
        this.pageHeaderService.setSidebarHeaderTemplate(null);

        // Деактивираме fullscreen режим
        this.store.dispatch(
            ClientUiActions.toggleFullscreenLayout({ isFullscreen: false })
        );
    }

    initContractDetailsDrawerConfig() {
        this.store.dispatch(
            ContractUiActions.changeDrawerConfigData({
                width: "70%",
                placement: "right",
                offsetX: 0,
            })
        );
    }

    loadContractsData(page: number = 1): void {
        this.loading.set(true);
        this.page.set(page);

        if (!this.filterParams()) {
            this.loading.set(false);
            console.error("Filter params are not set");
            return;
        }

        this.documentsRenewalService
            .getDocumentsForRenewal(this.documentType, this.filterParams(), {
                pageIndex: page,
                pageSize: this.pageSize,
            })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
                next: (response) => {
                    this.contractsData.set(response);
                    this.loading.set(false);
                },
                error: (error) => {
                    this.store.dispatch(
                        MessageActions.showMessage({
                            messageType: MessageTypeEnum.ERROR,
                            message: this.translateService.instant(
                                ErrorMessagesEnum.ERROR_LOADING_DOCUMENTS_LIST
                            ),
                        })
                    );
                    this.loading.set(false);
                    // Reset to empty state on error
                    this.contractsData.set({
                        rows: [],
                        total: 0,
                        footer: null,
                        columns: [],
                    });
                },
            });
    }

    onBackClick(): void {
        // Навигация обратно към съответната листа
        if (this.documentType === DocumentTypeEnum.Contracts) {
            this.router.navigate(["/client/documents/contracts"]);
        } else if (this.documentType === DocumentTypeEnum.Subleases) {
            this.router.navigate(["/client/documents/subleases"]);
        }
    }

    switchPlotsFilter(filter: RenewalPlotsFilterEnum, reloadData = true): void {
        this.store.dispatch(
            ContractsRenewalFilterUiActions.setFilterValue({
                documentType: this.documentType,
                key: "can_renew",
                value: RenewalPlotsFilterMap.get(filter),
            })
        );

        if (reloadData) {
            this.loadContractsData(1);
        }
    }
}
