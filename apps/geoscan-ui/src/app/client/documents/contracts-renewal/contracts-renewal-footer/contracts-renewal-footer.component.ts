import {
    ChangeDetectionStrategy,
    Component,
    inject,
    input,
} from "@angular/core";
import { MainNavSelectors } from "@geoscan/main-lib/state/main-nav";
import { ContractRenewalFooterItemType } from "@geoscan/main-lib/types";
import { Store } from "@ngrx/store";
import { Observable } from "rxjs";

@Component({
    selector: "gs-contracts-renewal-footer",
    templateUrl: "./contracts-renewal-footer.component.html",
    styleUrl: "./contracts-renewal-footer.component.scss",
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractsRenewalFooterComponent {
    private store = inject(Store);

    readonly footerData = input<ContractRenewalFooterItemType[]>([]);
    readonly loading = input<boolean>(false);

    mainNavWidth$: Observable<number> = this.store.select(
        MainNavSelectors.selectMainNavWidth
    );
}
