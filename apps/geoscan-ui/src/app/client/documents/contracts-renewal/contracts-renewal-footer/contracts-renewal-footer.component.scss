@use "../../../../../styles/rem-calc";

.info-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid var(--border-primary-color);
    height: var(--attribute-info-footer-height);
    background-color: var(--bg-primary-color);

    & > div {
        flex-direction: column;
        justify-content: center;
        border-left: 1px solid var(--border-primary-color);

        &:first-child {
            border-left: none;
        }
    }

    .label {
        margin-bottom: rem-calc.rem-calc(4);
        color: var(--text-light-grey-color);
        line-height: var(--line-height-sm);
        font: {
            size: var(--font-sm);
            family: var(--font-medium);
        }
    }
}
