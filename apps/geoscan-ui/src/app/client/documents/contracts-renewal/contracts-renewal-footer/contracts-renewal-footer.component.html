<div
    nz-row
    nzAlign="middle"
    class="info-footer footer"
    [style.left.px]="mainNavWidth$ | async"
>
    <div nz-col nzFlex="286px" class="font-lg font-medium pl-lg">
        {{ "Total area" | translate }} ({{ "dka" | translate }})
    </div>

    @if (footerData()) {
        @for (footer of footerData(); track footer.title) {
            <div nz-col [nzFlex]="1" class="pl-lg">
                <div class="label">
                    {{ footer.title | translate }}
                    @if (footer.year) {
                        - {{ footer.year }}
                    }
                </div>

                @if (!loading()) {
                    <div class="font-bold">
                        {{ footer.value | number: "1.0-3" | dashIfEmpty }}
                    </div>
                } @else {
                    <span
                        nz-icon
                        nzType="loading"
                        nzTheme="outline"
                        class="align-left"
                    ></span>
                }
            </div>
        }
    }
</div>
