@use "../../../../styles/rem-calc";

// Segmented component styling
.client-segmented {
    ::ng-deep {
        .ant-segmented-item {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.info-icon {
    font-size: var(--font-xl);
}

.fill-box {
    display: inline-block;
    margin-right: var(--margin-xs);
    width: rem-calc.rem-calc(14);
    height: rem-calc.rem-calc(14);
    border-radius: 4px;
    flex: none;
}

.yellow-fill-box {
    border: rem-calc.rem-calc(1) solid var(--border-orange-color);
    background-color: var(--bg-yellow-color);
}

.red-fill-box {
    border: rem-calc.rem-calc(1) solid var(--secondary-color-red);
    background-color: var(--secondary-color-pink);
}
