import { createAction, props } from "@ngrx/store";
import {
    DocumentTypeEnum,
    FilterFormControlValueType,
} from "@geoscan/main-lib/types";

export const setFilter = createAction(
    "[Contracts Renewal Filter UI] Set Filter",
    props<{
        documentType: DocumentTypeEnum.Contracts | DocumentTypeEnum.Subleases;
        value: { [key: string]: FilterFormControlValueType };
    }>()
);

export const setFilterValue = createAction(
    "[Contracts Renewal Filter UI] Set Filter Value",
    props<{
        documentType: DocumentTypeEnum.Contracts | DocumentTypeEnum.Subleases;
        key: string;
        value: FilterFormControlValueType;
    }>()
);

export const clearFilter = createAction(
    "[Contracts Renewal Filter UI] Clear Filter",
    props<{
        documentType: DocumentTypeEnum.Contracts | DocumentTypeEnum.Subleases;
    }>()
);

export const clearAllFilters = createAction(
    "[Contracts Renewal Filter UI] Clear All Filters"
);
