import {
    ChangeDetectionStrategy,
    Component,
    computed,
    inject,
    On<PERSON><PERSON>roy,
    OnInit,
    signal,
} from "@angular/core";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { FormGroup } from "@angular/forms";
import { HelperService } from "@geoscan/main-lib/services";
import {
    DocumentTypeEnum,
    FilterFormControl,
    FilterTypeEnum,
    RenewalPlotsFilterEnum,
    RenewalPlotsFilterMap,
} from "@geoscan/main-lib/types";
import { NzDrawerRef, NzDrawerService } from "ng-zorro-antd/drawer";
import { distinctUntilChanged } from "rxjs";
import { BaseContractsRenewalFilterComponent } from "../base-contracts-renewal-filter/base-contracts-renewal-filter.component";
import { ContractsRenewalVerticalFilterComponent } from "../contracts-renewal-vertical-filter/contracts-renewal-vertical-filter.component";

@Component({
    selector: "gs-contracts-renewal-horizontal-filter",
    templateUrl: "./contracts-renewal-horizontal-filter.component.html",
    styleUrls: ["./contracts-renewal-horizontal-filter.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractsRenewalHorizontalFilterComponent
    extends BaseContractsRenewalFilterComponent
    implements OnInit, OnDestroy
{
    filterControls = signal<FilterFormControl[]>([]);
    numberOfFilters = computed<number>(
        () =>
            Object.entries(this.filterParams() ?? {}).filter(
                ([key, value]) =>
                    (Array.isArray(value) && value.length > 0) ||
                    (!Array.isArray(value) &&
                        value !== undefined &&
                        value !== null &&
                        key !== "can_renew") // do not count the hidden control
            ).length ?? null
    );

    private drawerService = inject(NzDrawerService);
    private helperService = inject(HelperService);
    private verticalFilterDrawerRef: NzDrawerRef;

    ngOnInit(): void {
        super.ngOnInit();
        this.initializeFilterControls();
    }

    ngOnDestroy(): void {
        this.verticalFilterDrawerRef?.close();
    }

    showAllFilters() {
        this.verticalFilterDrawerRef =
            this.drawerService.create<ContractsRenewalVerticalFilterComponent>({
                nzTitle: "Filter",
                nzContent: ContractsRenewalVerticalFilterComponent,
                nzData: {
                    documentType: this.documentType,
                    onApplyFilters: () => {
                        this.onFilterParamsChange.emit();
                    },
                },
                nzWidth: 320,
                nzWrapClassName: "filters-form-drawer",
                nzClosable: false,
                nzMask: false,
            });
    }

    initializeFilterControls(): void {
        const documentFilterControlsByTypeMap = new Map<
            DocumentTypeEnum.Contracts | DocumentTypeEnum.Subleases,
            FilterFormControl[]
        >([
            [DocumentTypeEnum.Contracts, this.getContractsFilterControls()],
            [DocumentTypeEnum.Subleases, this.getSharedFilterControls()],
        ]);

        const controls = documentFilterControlsByTypeMap.get(
            this.documentType()
        );

        this.filterControls.set(controls);
    }

    clearFilters() {
        this.filterControls().forEach((control) => {
            if (control.getOptions()?.name === "farming_year") {
                control.setValue(this.helperService.getCurrentFarmYearText(), {
                    emitEvent: false,
                });
                return;
            }

            if (control.getOptions()?.name === "can_renew") {
                return;
            }

            control.setValue(undefined, { emitEvent: false });
        });

        this.form.updateValueAndValidity();
    }

    onFormInit(form: FormGroup) {
        this.form = form;
        this.setFilterToState(this.form.getRawValue());
        this.onFilterParamsChange.emit();

        this.form.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef), distinctUntilChanged())
            .subscribe({
                next: (value) => {
                    this.setFilterToState(value);
                    this.onFilterParamsChange.emit();
                },
            });
    }

    private getSharedFilterControls(): FilterFormControl[] {
        return [
            new FilterFormControl({
                name: "can_renew",
                type: FilterTypeEnum.Hidden,
                label: "Can renew",
                value:
                    this.getFilterValue("can_renew") ??
                    RenewalPlotsFilterMap.get(RenewalPlotsFilterEnum.All),
                disabled: false,
            }),

            new FilterFormControl({
                name: "farming_year",
                type: FilterTypeEnum.Select,
                label: "Farming year",
                allowClear: false,
                value:
                    this.getFilterValue("farming_year") ??
                    this.helperService.getCurrentFarmYearText(),
                disabled: false,
                items: [],
                multiple: false,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),

            new FilterFormControl({
                name: "farming_name",
                type: FilterTypeEnum.Select,
                label: "Farming",
                allowClear: true,
                value: this.getFilterValue("farming_name"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),

            new FilterFormControl({
                name: "kad_ident",
                type: FilterTypeEnum.Select,
                label: "Identifier",
                allowClear: true,
                value: this.getFilterValue("kad_ident"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),
        ];
    }

    private getContractsFilterControls(): FilterFormControl[] {
        return [
            ...this.getSharedFilterControls(),

            new FilterFormControl({
                name: "virtual_contract_type",
                type: FilterTypeEnum.Select,
                label: "Contract type",
                allowClear: true,
                value: this.getFilterValue("virtual_contract_type"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),
        ];
    }
}
