import { createReducer, on } from "@ngrx/store";
import { ContractsRenewalFilterUiActions } from "../actions";
import {
    ContractsRenewalFilterState,
    initialContractsRenewalFilterState,
} from "../contracts-renewal-filter.state";

export const contractsRenewalFilterReducer = createReducer(
    initialContractsRenewalFilterState,
    on(
        ContractsRenewalFilterUiActions.setFilter,
        (state, { documentType, value }): ContractsRenewalFilterState => {
            return {
                ...state,
                [documentType]: value,
            };
        }
    ),
    on(
        ContractsRenewalFilterUiActions.setFilterValue,
        (state, { documentType, key, value }): ContractsRenewalFilterState => {
            return {
                ...state,
                [documentType]: {
                    ...state[documentType],
                    [key]: value,
                },
            };
        }
    ),
    on(
        ContractsRenewalFilterUiActions.clearFilter,
        (state, { documentType }): ContractsRenewalFilterState => {
            return {
                ...state,
                [documentType]: {},
            };
        }
    ),
    on(
        ContractsRenewalFilterUiActions.clearAllFilters,
        (): ContractsRenewalFilterState => initialContractsRenewalFilterState
    )
);
