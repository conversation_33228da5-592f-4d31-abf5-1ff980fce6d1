<ng-template #drawerTitle>
    <div nz-row nzAlign="middle" class="flex-no-wrap">
        <div nz-col nzFlex="auto">
            <div class="display-flex flex-align-center">
                {{ "Filters" | translate }}
                <span
                    class="gs-icon-info-circle ml-xs"
                    nz-tooltip
                    [nzTooltipTitle]="
                        'Type part of the value in the input and select from the list. You can select multiple values'
                            | translate
                    "
                ></span>
            </div>
        </div>
        <div nz-col nzFlex="35px">
            <button
                nz-button
                nzType="default"
                class="gs-btn-icon ml-auto"
                (click)="closeWithoutChanges()"
            >
                <span class="gs-icon-cross"></span>
            </button>
        </div>
    </div>
</ng-template>

<gs-vertical-filter-form
    [filterControlsBySection]="filterControlsBySection()"
    [activeSection]="activeSection()"
    [size]="componentSize()"
    (formInit)="onFormInit($event)"
>
</gs-vertical-filter-form>

<div nz-row nzGutter="12" class="p-lg footer">
    <div nz-col nzSpan="12">
        <button
            nz-button
            nzType="default"
            class="w-100"
            (click)="clearFilters()"
            [nzSize]="componentSize()"
        >
            {{ "Clear" | translate }}
        </button>
    </div>
    <div nz-col nzSpan="12">
        <button
            nz-button
            nzType="primary"
            class="w-100"
            (click)="applyFilters()"
            [nzSize]="componentSize()"
        >
            {{ "Show" | translate }}
        </button>
    </div>
</div>
