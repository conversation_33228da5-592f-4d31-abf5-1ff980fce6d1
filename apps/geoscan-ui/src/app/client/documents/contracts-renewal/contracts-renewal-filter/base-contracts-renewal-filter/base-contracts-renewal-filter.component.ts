import {
    Component,
    DestroyRef,
    inject,
    input,
    OnInit,
    output,
    Signal,
    signal,
} from "@angular/core";
import { takeUntilDestroyed, toSignal } from "@angular/core/rxjs-interop";
import { FormGroup } from "@angular/forms";
import { DocumentsRenewalService } from "@geoscan/main-lib/services";
import {
    BaseResponseType,
    DocumentTypeEnum,
    FilterFormControlValueType,
    LabelValue,
} from "@geoscan/main-lib/types";
import { Store } from "@ngrx/store";
import { cloneDeep } from "lodash";
import moment from "moment";
import { NzSizeLDSType } from "ng-zorro-antd/core/types";
import { distinctUntilChanged, Observable } from "rxjs";
import { ContractsRenewalFilterUiActions } from "../+state/actions";
import { ContractsRenewalFilterSelectors } from "../+state/selectors";
import { NgZorroConfigSelectors } from "../../../../../+state/selectors";

@Component({
    template: "",
})
export class BaseContractsRenewalFilterComponent implements OnInit {
    documentType = input<
        DocumentTypeEnum.Contracts | DocumentTypeEnum.Subleases
    >();

    onFilterParamsChange = output<void>();

    protected filterParams = signal<{
        [key: string]: FilterFormControlValueType;
    }>({});
    protected form: FormGroup;
    protected store = inject(Store);
    protected componentSize: Signal<NzSizeLDSType>;
    protected destroyRef = inject(DestroyRef);

    private documentsRenewalService = inject(DocumentsRenewalService);

    constructor() {
        this.initializeComponentSize();
    }

    ngOnInit(): void {
        this.initializeFilterParams();
    }

    /**
     * This method sets the value of the filter params in the state.
     *
     * @param value
     */
    protected setFilterToState(value: {
        [key: string]: FilterFormControlValueType;
    }) {
        value = Object.entries(cloneDeep(value || {})).reduce(
            (acc, [key, value]) => {
                if (value instanceof Date) {
                    value = moment(value as Date).format("YYYY-MM-DD");
                }

                return {
                    ...acc,
                    [key]: value,
                };
            },
            {}
        );

        this.store.dispatch(
            ContractsRenewalFilterUiActions.setFilter({
                documentType: this.documentType(),
                value,
            })
        );
    }

    protected loadItemsFn = (
        controlName: string,
        pageIndex: number,
        pageSize: number,
        search?: string
    ): Observable<BaseResponseType<LabelValue<string>>> => {
        const filterParams = cloneDeep(this.filterParams());

        if (filterParams[controlName]) {
            // Delete the filter by this control from the filterParams
            // to avoid sending its value to the server, cause it will cause returning only the selected items
            delete filterParams[controlName];
        }

        return this.documentsRenewalService.getFilterItems(
            this.documentType(),
            controlName,
            filterParams,
            { pageIndex, pageSize },
            search
        );
    };

    /**
     * This method retrieves the value of a specific filter key from the state and returns it.
     * If the key does not exist, it returns undefined.
     *
     * @template T - The expected type of the filter value.
     * @param key - The key of the filter to retrieve.
     * @returns The value of the filter key, or undefined if it does not exist.
     */
    protected getFilterValue<T>(key: string): T {
        return (this.filterParams()[key] as T) ?? undefined;
    }

    private initializeFilterParams() {
        this.store
            .select(
                ContractsRenewalFilterSelectors.selectContractsRenewalFilterByType(
                    this.documentType()
                )
            )
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
                next: (params) => {
                    // Update the filterParams
                    this.filterParams.set(params);

                    // Update the local form value from the state without emitting event in order to avoid infinite loop
                    this.form?.patchValue(params, { emitEvent: false });
                },
            });
    }

    private initializeComponentSize() {
        this.componentSize = toSignal(
            this.store
                .select(NgZorroConfigSelectors.selectComponentSize)
                .pipe(distinctUntilChanged())
        );
    }
}
