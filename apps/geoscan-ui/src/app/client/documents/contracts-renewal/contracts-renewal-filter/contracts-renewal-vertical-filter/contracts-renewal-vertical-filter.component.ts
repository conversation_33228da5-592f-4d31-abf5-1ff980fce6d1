import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    inject,
    OnInit,
    signal,
    TemplateRef,
    ViewChild,
} from "@angular/core";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { FormGroup } from "@angular/forms";
import { HelperService } from "@geoscan/main-lib/services";
import {
    DocumentFilterSectionEnum,
    DocumentTypeEnum,
    FilterFormControl,
    FilterFormControlValueType,
    FilterTypeEnum,
    FormModeEnum,
    RenewalPlotsFilterEnum,
    RenewalPlotsFilterMap,
} from "@geoscan/main-lib/types";
import { flatMap } from "lodash";
import { NZ_DRAWER_DATA, NzDrawerRef } from "ng-zorro-antd/drawer";
import { distinctUntilChanged } from "rxjs/operators";
import { BaseContractsRenewalFilterComponent } from "../base-contracts-renewal-filter/base-contracts-renewal-filter.component";

@Component({
    selector: "gs-contracts-renewal-vertical-filter",
    templateUrl: "./contracts-renewal-vertical-filter.component.html",
    styleUrls: ["./contracts-renewal-vertical-filter.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractsRenewalVerticalFilterComponent
    extends BaseContractsRenewalFilterComponent
    implements OnInit, AfterViewInit
{
    @ViewChild("drawerTitle") drawerTitle: TemplateRef<unknown>;

    filterControlsBySection = signal<{
        [section: string]: FilterFormControl[];
    }>({});
    activeSection = signal<string>(DocumentFilterSectionEnum.Main.toString());

    formModeEnum = FormModeEnum;

    private drawerRef = inject(NzDrawerRef);
    private drawerData = inject(NZ_DRAWER_DATA) as {
        documentType: () =>
            | DocumentTypeEnum.Contracts
            | DocumentTypeEnum.Subleases;
        onApplyFilters?: () => void;
    };
    private initialFilterParams = signal<{
        [key: string]: FilterFormControlValueType;
    }>({});
    private helperService = inject(HelperService);

    ngOnInit(): void {
        super.ngOnInit();
        this.initializeFilterControlsBySection();
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.drawerRef.nzTitle = this.drawerTitle;
        });
    }

    onFormInit(form: FormGroup) {
        this.form = form;
        this.initialFilterParams.set(this.form.getRawValue());
        this.setFilterToState(this.form.getRawValue());
        this.onFilterParamsChange.emit();

        this.form.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef), distinctUntilChanged())
            .subscribe({
                next: (value) => {
                    this.setFilterToState(value);
                },
            });
    }

    clearFilters() {
        flatMap(this.filterControlsBySection()).forEach((control) => {
            if (control.getOptions()?.name === "farming_year") {
                control.setValue(this.helperService.getCurrentFarmYearText());
                return;
            }

            if (control.getOptions()?.name === "can_renew") {
                return;
            }

            control.setValue(undefined);
        });
    }

    applyFilters(): void {
        // Also call the callback function passed from the horizontal filter
        if (this.drawerData.onApplyFilters) {
            this.drawerData.onApplyFilters();
        }
        this.close();
    }

    closeWithoutChanges() {
        this.setFilterToState(this.initialFilterParams());
        this.close();
    }

    close() {
        this.drawerRef.close();
    }

    private initializeFilterControlsBySection(): void {
        const filterControls = this.generateFilterControls();

        this.filterControlsBySection.set(filterControls);
    }

    private generateFilterControls(): {
        [section: string]: FilterFormControl[];
    } {
        return {
            [DocumentFilterSectionEnum.Main]: [
                new FilterFormControl({
                    name: "can_renew",
                    type: FilterTypeEnum.Hidden,
                    label: "Can renew",
                    value:
                        this.getFilterValue("can_renew") ??
                        RenewalPlotsFilterMap.get(RenewalPlotsFilterEnum.All),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "farming_year",
                    type: FilterTypeEnum.Select,
                    label: "Farming year",
                    allowClear: false,
                    value:
                        this.getFilterValue("farming_year") ??
                        this.helperService.getCurrentFarmYearText(),
                    disabled: false,
                    items: [],
                    multiple: false,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "farming_name",
                    type: FilterTypeEnum.Select,
                    label: "Farming",
                    allowClear: true,
                    value: this.getFilterValue("farming_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "kad_ident",
                    type: FilterTypeEnum.Select,
                    label: "Identifier",
                    allowClear: true,
                    value: this.getFilterValue("kad_ident"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                ...(this.documentType() === DocumentTypeEnum.Contracts
                    ? [
                          new FilterFormControl({
                              name: "virtual_contract_type",
                              type: FilterTypeEnum.Select,
                              label: "Contract type",
                              allowClear: true,
                              value: this.getFilterValue(
                                  "virtual_contract_type"
                              ),
                              disabled: false,
                              items: [],
                              multiple: true,
                              loadItems: this.loadItemsFn,
                          }),
                      ]
                    : []),
                new FilterFormControl({
                    name: "virtual_ekatte_name",
                    type: FilterTypeEnum.Select,
                    label: "EKATTE",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ekatte_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "cnum",
                    type: FilterTypeEnum.Select,
                    label: "Document number",
                    allowClear: true,
                    value: this.getFilterValue("cnum"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                ...(this.documentType() === DocumentTypeEnum.Contracts
                    ? [
                          new FilterFormControl({
                              name: "contragent",
                              type: FilterTypeEnum.Select,
                              label: "Contragent",
                              allowClear: true,
                              value: this.getFilterValue("contragent"),
                              disabled: false,
                              items: [],
                              multiple: true,
                              loadItems: this.loadItemsFn,
                          }),
                      ]
                    : [
                          new FilterFormControl({
                              name: "tenant",
                              type: FilterTypeEnum.Select,
                              label: "Tenant",
                              allowClear: true,
                              value: this.getFilterValue("tenant"),
                              disabled: false,
                              items: [],
                              multiple: true,
                              loadItems: this.loadItemsFn,
                          }),
                      ]),
            ],
        };
    }
}
