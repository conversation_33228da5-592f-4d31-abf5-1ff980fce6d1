import {
    DocumentTypeEnum,
    FilterFormControlValueType,
} from "@geoscan/main-lib/types";

export type ContractsRenewalFilterState = {
    [DocumentTypeEnum.Contracts]: {
        [key: string]: FilterFormControlValueType;
    };
    [DocumentTypeEnum.Subleases]: {
        [key: string]: FilterFormControlValueType;
    };
};

export const initialContractsRenewalFilterState: ContractsRenewalFilterState = {
    [DocumentTypeEnum.Contracts]: {},
    [DocumentTypeEnum.Subleases]: {},
};
