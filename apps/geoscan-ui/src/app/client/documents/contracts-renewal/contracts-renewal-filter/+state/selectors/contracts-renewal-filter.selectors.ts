import { DocumentTypeEnum } from "@geoscan/main-lib/types";
import { createFeatureSelector, createSelector } from "@ngrx/store";
import { ContractsRenewalFilterState } from "../contracts-renewal-filter.state";

const selectContractsRenewalFiltersState =
    createFeatureSelector<ContractsRenewalFilterState>(
        "contractsRenewal.Filter"
    );

export const selectContractsRenewalFilterByType = (
    documentType: DocumentTypeEnum.Contracts | DocumentTypeEnum.Subleases
) =>
    createSelector(
        selectContractsRenewalFiltersState,
        (state) => state[documentType] || {}
    );

export const selectContractsRenewalFilters = createSelector(
    selectContractsRenewalFiltersState,
    (state) => state
);

export const selectContractsFilter = createSelector(
    selectContractsRenewalFiltersState,
    (state) => state[DocumentTypeEnum.Contracts] || {}
);

export const selectSubleasesFilter = createSelector(
    selectContractsRenewalFiltersState,
    (state) => state[DocumentTypeEnum.Subleases] || {}
);
