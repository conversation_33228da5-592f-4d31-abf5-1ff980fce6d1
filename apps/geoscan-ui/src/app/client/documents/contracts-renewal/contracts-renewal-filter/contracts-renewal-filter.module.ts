import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { StoreModule } from "@ngrx/store";
import { SharedModule } from "../../../../shared/shared.module";
import { contractsRenewalFilterReducer } from "./+state/reducers";
import { ContractsRenewalHorizontalFilterComponent } from "./contracts-renewal-horizontal-filter/contracts-renewal-horizontal-filter.component";
import { ContractsRenewalVerticalFilterComponent } from "./contracts-renewal-vertical-filter/contracts-renewal-vertical-filter.component";

@NgModule({
    declarations: [
        ContractsRenewalHorizontalFilterComponent,
        ContractsRenewalVerticalFilterComponent,
    ],
    imports: [
        CommonModule,
        RouterModule,
        SharedModule,
        StoreModule.forFeature(
            "contractsRenewal.Filter",
            contractsRenewalFilterReducer
        ),
    ],
    exports: [
        ContractsRenewalHorizontalFilterComponent,
        ContractsRenewalVerticalFilterComponent,
    ],
})
export class ContractsRenewalFilterModule {}
