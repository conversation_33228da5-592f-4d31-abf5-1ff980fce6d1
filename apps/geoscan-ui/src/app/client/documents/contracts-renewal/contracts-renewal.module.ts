import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { SharedModule } from "../../../shared/shared.module";
import { ContractsRenewalFilterModule } from "./contracts-renewal-filter/contracts-renewal-filter.module";
import { ContractsRenewalFooterComponent } from "./contracts-renewal-footer/contracts-renewal-footer.component";
import { ContractsRenewalRoutingModule } from "./contracts-renewal-routing.module";
import { ContractRenewalItemComponent } from "./contracts-renewal-table/contract-renewal-item/contract-renewal-item.component";
import { ContractsRenewalTableComponent } from "./contracts-renewal-table/contracts-renewal-table.component";
import { ContractsRenewalComponent } from "./contracts-renewal.component";

@NgModule({
    declarations: [
        ContractsRenewalComponent,
        ContractsRenewalTableComponent,
        ContractsRenewalFooterComponent,
        ContractRenewalItemComponent,
    ],
    imports: [
        CommonModule,
        RouterModule,
        SharedModule,
        ContractsRenewalFilterModule,
        ContractsRenewalRoutingModule,
    ],
    exports: [ContractsRenewalComponent],
})
export class ContractsRenewalModule {}
