<div #sidebarHeader>
    <nz-page-header class="pl-xs">
        <nz-page-header-title class="font-xxl l-h-xxl">
            <button
                nz-button
                nzType="text"
                nzSize="large"
                class="back-button"
                (click)="onBackClick()"
            >
                <span nz-icon nzType="arrow-left"></span>
            </button>

            {{ "Contract renewal data" | translate }}
            <span
                nz-icon
                nzType="info-circle"
                class="info-icon ml-xs"
                nz-tooltip
                [nzTooltipTitle]="
                    'This page shows the properties for which the leased/rented area in the current year is less than that in the following year'
                        | translate
                "
                nzTooltipPlacement="bottom"
            ></span>
        </nz-page-header-title>
    </nz-page-header>
</div>

<!-- Filters Section -->
<div class="mx-lg">
    <div class="filters-section mt-lg">
        <gs-contracts-renewal-horizontal-filter
            [documentType]="documentType"
            (onFilterParamsChange)="loadContractsData()"
        ></gs-contracts-renewal-horizontal-filter>
    </div>

    <div nz-row nzAlign="middle" class="my-lg">
        <div nz-col nzSpan="8">
            <button
                nz-button
                nzType="primary"
                class="mr-sm"
                [disabled]="noDataOrLoading()"
            >
                {{ "Renewal" | translate }}
            </button>
            <label nz-checkbox [nzDisabled]="noDataOrLoading()">{{
                "Select all renewals" | translate
            }}</label>
        </div>
        <div nz-col nzSpan="12">
            <span class class="mr-sm">{{ "Show plots" | translate }}</span>

            <nz-segmented
                class="client-segmented"
                [ngModel]="selectedPlotsFilter()"
                [nzLabelTemplate]="plotsTemplateRef"
                [nzOptions]="plotsOptions"
                [nzDisabled]="loading()"
                (ngModelChange)="switchPlotsFilter($event)"
            ></nz-segmented>

            <ng-template #plotsTemplateRef let-index="index">
                @switch (index) {
                    @case (renewalPlotsFilterEnum.All) {
                        <span>{{ "All" | translate }}</span>
                    }
                    @case (renewalPlotsFilterEnum.WithAutomaticRenewal) {
                        <div class="display-flex flex-align-center">
                            <span class="fill-box yellow-fill-box"></span>
                            <span>{{
                                "With automatic renewal" | translate
                            }}</span>
                        </div>
                    }
                    @case (renewalPlotsFilterEnum.WithoutAutomaticRenewal) {
                        <div class="display-flex flex-align-center">
                            <span class="fill-box red-fill-box"></span>
                            <span>{{
                                "Without automatic renewal" | translate
                            }}</span>
                        </div>
                    }
                }
            </ng-template>
        </div>
        <div nz-col nzSpan="4" class="align-right">
            <span class="total-count">{{ "Total count" | translate }}: </span
            ><span class="font-bold">{{ total() }}</span>
        </div>
    </div>

    <gs-contracts-renewal-table
        [contractsData]="contractsData()"
        [loading]="loading()"
        [pageIndex]="page()"
        [pageSize]="pageSize"
        (pageSizeChange)="loadContractsData($event)"
    ></gs-contracts-renewal-table>
</div>

@if (footer()?.length > 0) {
    <gs-contracts-renewal-footer
        [footerData]="footer()"
        [loading]="loading()"
    ></gs-contracts-renewal-footer>
}
