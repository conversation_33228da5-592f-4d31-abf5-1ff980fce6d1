<div nz-row nzAlign="middle" class="toolbar">
    <div nz-col nzSpan="15" class="display-flex flex-align-center">
        <button nz-button nzType="primary">
            {{ "Add" | translate }}
        </button>

        <!-- <PERSON><PERSON> Button - Only for Contracts and Subleases -->
        @if (shouldShowRenewalButton()) {
            <nz-divider
                nzType="vertical"
                class="toolbar-divider mx-sm"
            ></nz-divider>

            <button
                nz-button
                nz-tooltip
                [nzTooltipTitle]="'Contract renewal' | translate"
                nzTooltipPlacement="top"
                class="gs-btn-icon"
                (click)="onRenewalClick()"
            >
                <gs-icon>
                    <span class="gs-icon-renew"></span>
                </gs-icon>
            </button>

            <nz-divider
                nzType="vertical"
                class="toolbar-divider ml-sm mr-0"
            ></nz-divider>
        }

        <!--Copy-->
        <button
            nz-button
            nz-tooltip
            [nzTooltipTitle]="'Copy' | translate"
            nzTooltipPlacement="top"
            class="gs-btn-icon ml-sm"
        >
            <gs-icon>
                <span class="gs-icon-copy"></span>
            </gs-icon>
        </button>
        <!--Annex-->
        <button
            nz-button
            nz-tooltip
            [nzTooltipTitle]="'Annex' | translate"
            nzTooltipPlacement="top"
            nzTrigger="click"
            class="gs-btn-icon ml-sm"
        >
            <gs-icon>
                <span class="gs-icon-annex"></span>
            </gs-icon>
        </button>
        <!--Annul-->
        <button
            nz-button
            nz-tooltip
            nz-popconfirm
            [nzTooltipTitle]="'Annul' | translate"
            nzTooltipPlacement="top"
            [nzPopconfirmTitle]="
                'This operation could take a while' | translate
            "
            nzPopconfirmPlacement="topRight"
            nzOkText="{{ 'Ok' | translate }}"
            nzCancelText="{{ 'Cancel' | translate }}
            "
            class="gs-btn-icon ml-sm"
        >
            <span nz-icon nzType="close-circle" nzTheme="outline"></span>
        </button>
        <!--Delete-->
        <button
            nz-button
            nz-tooltip
            [nzTooltipTitle]="'Delete' | translate"
            nzTooltipPlacement="top"
            nz-popconfirm
            [nzPopconfirmTitle]="
                'Are you sure you want to delete these features?' | translate
            "
            nzPopconfirmPlacement="bottom"
            nzOkText="{{ 'Ok' | translate }}"
            nzCancelText="{{ 'Cancel' | translate }}"
            class="gs-btn-icon ml-sm"
        >
            <gs-icon>
                <span class="gs-icon-delete"></span>
            </gs-icon>
        </button>

        <!--MultiEdit-->
        <button
            nz-button
            nz-tooltip
            [nzTooltipTitle]="'Multiedit' | translate"
            nzTooltipPlacement="top"
            class="gs-btn-icon ml-sm"
        >
            <gs-icon>
                <span class="gs-icon-multiedit"></span>
            </gs-icon>
        </button>
        <!--Print-->
        <button
            nz-button
            nz-tooltip
            [nzTooltipTitle]="'Print' | translate"
            nzTooltipPlacement="top"
            nzPopconfirmPlacement="bottom"
            class="gs-btn-icon ml-sm"
        >
            <gs-icon>
                <span class="gs-icon-print"></span>
            </gs-icon>
        </button>
    </div>
    <div
        nz-col
        nzSpan="9"
        class="display-flex flex-align-center flex-justify-content-right"
    >
        <div class="font-medium">
            {{ "Total count" | translate }}: {{ totalCount() }}
        </div>
        <button
            nz-button
            nz-tooltip
            nz-dropdown
            nzTrigger="click"
            class="gs-btn-icon ml-sm"
        >
            <gs-icon>
                <span class="gs-icon-file-excel"></span>
            </gs-icon>
        </button>
    </div>
</div>
