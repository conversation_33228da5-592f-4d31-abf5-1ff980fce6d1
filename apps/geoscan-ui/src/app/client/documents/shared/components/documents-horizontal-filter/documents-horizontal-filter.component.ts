import {
    ChangeDetectionStrategy,
    Component,
    computed,
    inject,
    On<PERSON><PERSON>roy,
    OnInit,
    signal,
} from "@angular/core";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { FormGroup } from "@angular/forms";
import {
    DocumentTypeEnum,
    FilterFormControl,
    FilterTypeEnum,
} from "@geoscan/main-lib/types";
import { NzDrawerRef, NzDrawerService } from "ng-zorro-antd/drawer";
import { distinctUntilChanged } from "rxjs";
import { BaseDocumentsFilterComponent } from "../base-documents-filter/base-documents-filter.component";
import { DocumentsVerticalFilterComponent } from "../documents-vertical-filter/documents-vertical-filter.component";

@Component({
    selector: "gs-documents-horizontal-filter",
    templateUrl: "./documents-horizontal-filter.component.html",
    styleUrls: ["./documents-horizontal-filter.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentsHorizontalFilterComponent
    extends BaseDocumentsFilterComponent
    implements OnInit, OnDestroy
{
    filterControls = signal<FilterFormControl[]>([]);
    numberOfFilters = computed<number>(
        () =>
            Object.values(this.filterParams() ?? {}).filter(
                (value) =>
                    (Array.isArray(value) && value.length > 0) ||
                    (!Array.isArray(value) &&
                        value !== undefined &&
                        value !== null)
            ).length ?? null
    );

    private drawerService = inject(NzDrawerService);
    private verticalFilterDrawerRef: NzDrawerRef;

    ngOnInit(): void {
        super.ngOnInit();
        this.initializeFilterControls();
    }

    ngOnDestroy(): void {
        this.verticalFilterDrawerRef?.close();
    }

    showAllFilters() {
        this.verticalFilterDrawerRef =
            this.drawerService.create<DocumentsVerticalFilterComponent>({
                nzTitle: "Filter",
                nzContent: DocumentsVerticalFilterComponent,
                nzData: {
                    documentType: this.documentType,
                },
                nzWidth: 320,
                nzWrapClassName: "filters-form-drawer",
                nzClosable: false,
                nzMask: false,
            });
    }

    initializeFilterControls(): void {
        const documentFilterControlsByTypeMap = new Map<
            DocumentTypeEnum,
            FilterFormControl[]
        >([
            [DocumentTypeEnum.Contracts, this.getContractsFilterControls()],
            [DocumentTypeEnum.Ownership, this.getOwnershipFilterControls()],
            [DocumentTypeEnum.Sales, this.getSalesFilterControls()],
            [DocumentTypeEnum.Mortgage, this.getMortgageFilterControls()],
            [DocumentTypeEnum.Subleases, this.getSubleasesFilterControls()],
        ]);

        const controls = documentFilterControlsByTypeMap.get(
            this.documentType()
        );

        this.filterControls.set(controls);
    }

    clearFilters() {
        this.filterControls().forEach((control) =>
            control.setValue(undefined, { emitEvent: false })
        );
        this.form.updateValueAndValidity();
    }

    onFormInit(form: FormGroup) {
        this.form = form;

        this.form.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef), distinctUntilChanged())
            .subscribe({
                next: (value) => {
                    this.setFilterToState(value);
                    this.reloadDocuments();
                },
            });
    }

    private getContractsFilterControls(): FilterFormControl[] {
        return [
            ...this.getSharedFilterControls(),

            new FilterFormControl({
                name: "farming_years",
                type: FilterTypeEnum.Select,
                label: "Farming year",
                allowClear: true,
                value: this.getFilterValue("farming_years"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),

            new FilterFormControl({
                name: "contragent",
                type: FilterTypeEnum.Select,
                label: "Contragent",
                allowClear: true,
                value: this.getFilterValue("contragent"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),
        ];
    }

    private getOwnershipFilterControls(): FilterFormControl[] {
        return [
            ...this.getSharedFilterControls(),

            new FilterFormControl({
                name: "farming_name",
                type: FilterTypeEnum.Select,
                label: "Farming",
                allowClear: true,
                value: this.getFilterValue("farming_name"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),
        ];
    }

    private getSalesFilterControls(): FilterFormControl[] {
        return [
            ...this.getSharedFilterControls(),

            new FilterFormControl({
                name: "buyer",
                type: FilterTypeEnum.Select,
                label: "Buyer",
                allowClear: true,
                value: this.getFilterValue("buyer"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),
        ];
    }

    private getMortgageFilterControls(): FilterFormControl[] {
        return [
            ...this.getSharedFilterControls(),

            new FilterFormControl({
                name: "creditor",
                type: FilterTypeEnum.Select,
                label: "Creditor",
                allowClear: true,
                value: this.getFilterValue("creditor"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),
        ];
    }

    private getSubleasesFilterControls(): FilterFormControl[] {
        return [
            ...this.getSharedFilterControls(),

            new FilterFormControl({
                name: "tenant",
                type: FilterTypeEnum.Select,
                label: "Tenant",
                allowClear: true,
                value: this.getFilterValue("tenant"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),
        ];
    }

    private getSharedFilterControls(): FilterFormControl[] {
        return [
            new FilterFormControl({
                name: "cnum",
                type: FilterTypeEnum.Select,
                label: "Document number",
                allowClear: true,
                value: this.getFilterValue("cnum"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),

            new FilterFormControl({
                name: "kad_ident",
                type: FilterTypeEnum.Select,
                label: "Identifier",
                allowClear: true,
                value: this.getFilterValue("kad_ident"),
                disabled: false,
                items: [],
                multiple: true,
                maxTagCount: 2,
                loadItems: this.loadItemsFn,
            }),
        ];
    }
}
