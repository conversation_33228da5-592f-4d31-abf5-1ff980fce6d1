@use "../../../../../../styles/rem-calc";
@use "../../../../../../styles/mixins";

$footer-height: #{rem-calc.rem-calc(80)};
$footer-height-small: #{rem-calc.rem-calc(60)};

:host {
    display: block;
    height: 100%;
}

.gs-btn-icon {
    &:hover {
        span[class^="gs-icon"] {
            background-color: var(--icon-primary-color);
        }
    }
}

.footer {
    border-top: 1px solid var(--border-primary-color);
}

.gs-icon-info-circle {
    width: rem-calc.rem-calc(16);
    height: rem-calc.rem-calc(16);
}

gs-vertical-filter-form {
    display: block;
    height: calc(100% - #{$footer-height});
    overflow: hidden auto;
    @include mixins.scrollbar;
}

@media screen and (max-width: 1600px) {
    gs-vertical-filter-form {
        height: calc(100% - #{$footer-height-small});
    }
}
