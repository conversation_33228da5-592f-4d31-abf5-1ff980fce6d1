import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    inject,
    OnInit,
    signal,
    TemplateRef,
    ViewChild,
} from "@angular/core";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { FormGroup } from "@angular/forms";
import {
    DocumentFilterSectionEnum,
    DocumentTypeEnum,
    FilterFormControl,
    FilterFormControlValueType,
    FilterTypeEnum,
    FormModeEnum,
} from "@geoscan/main-lib/types";
import { flatMap } from "lodash";
import { NzDrawerRef } from "ng-zorro-antd/drawer";
import { distinctUntilChanged } from "rxjs/operators";
import { BaseDocumentsFilterComponent } from "../base-documents-filter/base-documents-filter.component";

@Component({
    selector: "gs-documents-vertical-filter",
    templateUrl: "./documents-vertical-filter.component.html",
    styleUrls: ["./documents-vertical-filter.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentsVerticalFilterComponent
    extends BaseDocumentsFilterComponent
    implements OnInit, AfterViewInit
{
    @ViewChild("drawerTitle") drawerTitle: TemplateRef<unknown>;

    filterControlsBySection = signal<{
        [section: string]: FilterFormControl[];
    }>({});
    activeSection = signal<string>(
        DocumentFilterSectionEnum.Popular.toString()
    );

    formModeEnum = FormModeEnum;

    private drawerRef = inject(NzDrawerRef);
    private initialFilterParams = signal<{
        [key: string]: FilterFormControlValueType;
    }>({});

    ngOnInit(): void {
        super.ngOnInit();
        this.initializeFilterControlsBySection();
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.drawerRef.nzTitle = this.drawerTitle;
        });
    }

    onFormInit(form: FormGroup) {
        this.form = form;
        this.initialFilterParams.set(this.form.getRawValue());

        this.form.valueChanges
            .pipe(takeUntilDestroyed(this.destroyRef), distinctUntilChanged())
            .subscribe({
                next: (value) => {
                    this.setFilterToState(value);
                },
            });
    }

    clearFilters() {
        flatMap(this.filterControlsBySection()).forEach((control) =>
            control.setValue(undefined)
        );
    }

    applyFilters(): void {
        this.reloadDocuments();
        this.close();
    }

    closeWithoutChanges() {
        this.setFilterToState(this.initialFilterParams());
        this.close();
    }

    close() {
        this.drawerRef.close();
    }

    private initializeFilterControlsBySection(): void {
        const documentFilterControlsByTypeMap = new Map<
            DocumentTypeEnum,
            { [section: string]: FilterFormControl[] }
        >([
            [DocumentTypeEnum.Contracts, this.getContractsFilterControls()],
            [DocumentTypeEnum.Ownership, this.getOwnershipFilterControls()],
            [DocumentTypeEnum.Sales, this.getSalesFilterControls()],
            [DocumentTypeEnum.Mortgage, this.getMortgageFilterControls()],
            [DocumentTypeEnum.Subleases, this.getSubleasesFilterControls()],
        ]);

        const filterControls = documentFilterControlsByTypeMap.get(
            this.documentType()
        );

        this.filterControlsBySection.set(filterControls);
    }

    private getContractsFilterControls(): {
        [section: string]: FilterFormControl[];
    } {
        return {
            [DocumentFilterSectionEnum.Popular]: [
                new FilterFormControl({
                    name: "cnum",
                    type: FilterTypeEnum.Select,
                    label: "Document number",
                    allowClear: true,
                    value: this.getFilterValue("cnum"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "farming_years",
                    type: FilterTypeEnum.Select,
                    label: "Farming year",
                    allowClear: true,
                    value: this.getFilterValue("farming_years"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contragent",
                    type: FilterTypeEnum.Select,
                    label: "Contragent",
                    allowClear: true,
                    value: this.getFilterValue("contragent"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "expiring_contracts_for_farm_year",
                    type: FilterTypeEnum.Select,
                    label: "Properties with expiring contracts for",
                    allowClear: true,
                    value: this.getFilterValue(
                        "expiring_contracts_for_farm_year"
                    ),
                    disabled: false,
                    items: [],
                    multiple: false,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "kad_ident",
                    type: FilterTypeEnum.Select,
                    label: "Identifier",
                    allowClear: true,
                    value: this.getFilterValue("kad_ident"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Field]: [
                new FilterFormControl({
                    name: "virtual_ekatte_name",
                    type: FilterTypeEnum.Select,
                    label: "EKATTE",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ekatte_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "masiv",
                    type: FilterTypeEnum.Select,
                    label: "Masiv",
                    allowClear: true,
                    value: this.getFilterValue("masiv"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "number",
                    type: FilterTypeEnum.Select,
                    label: "Estate",
                    allowClear: true,
                    value: this.getFilterValue("number"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_category_title",
                    type: FilterTypeEnum.Select,
                    label: "Category",
                    allowClear: true,
                    value: this.getFilterValue("virtual_category_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_ntp_title",
                    type: FilterTypeEnum.Select,
                    label: "NTP",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ntp_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "mestnost",
                    type: FilterTypeEnum.Select,
                    label: "Local area",
                    allowClear: true,
                    value: this.getFilterValue("mestnost"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "allowable_type",
                    type: FilterTypeEnum.Select,
                    label: "Allowable type",
                    allowClear: true,
                    value: this.getFilterValue("allowable_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "irrigated_area",
                    type: FilterTypeEnum.Select,
                    label: "Irrigated area",
                    allowClear: true,
                    value: this.getFilterValue("irrigated_area"),
                    disabled: false,
                    items: [
                        { label: "Yes", value: true },
                        { label: "No", value: false },
                    ],
                    multiple: true,
                }),
                new FilterFormControl({
                    name: "participation",
                    type: FilterTypeEnum.Select,
                    label: "Participation art.37",
                    allowClear: true,
                    value: this.getFilterValue("participation"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Document]: [
                new FilterFormControl({
                    name: "virtual_contract_type",
                    type: FilterTypeEnum.Select,
                    label: "Contract type",
                    allowClear: true,
                    value: this.getFilterValue("virtual_contract_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "farming_name",
                    type: FilterTypeEnum.Select,
                    label: "Farming",
                    allowClear: true,
                    value: this.getFilterValue("farming_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contract_status_text",
                    type: FilterTypeEnum.Select,
                    label: "Status",
                    allowClear: true,
                    value: this.getFilterValue("contract_status_text"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contract_date_from",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract from date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_from"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "contract_date_to",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract To date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_to"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "group",
                    type: FilterTypeEnum.Select,
                    label: "Group",
                    allowClear: true,
                    value: this.getFilterValue("group"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "closed_for_editing",
                    type: FilterTypeEnum.Select,
                    label: "Closed for editing",
                    allowClear: true,
                    value: this.getFilterValue("closed_for_editing"),
                    disabled: false,
                    items: [
                        { label: "Yes", value: true },
                        { label: "No", value: false },
                    ],
                }),
                new FilterFormControl({
                    name: "notary_number",
                    type: FilterTypeEnum.Select,
                    label: "Notarial act number",
                    allowClear: true,
                    value: this.getFilterValue("notary_number"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "incomplete_ownership_details",
                    type: FilterTypeEnum.Select,
                    label: "Incomplete ownership details",
                    allowClear: true,
                    value: this.getFilterValue("incomplete_ownership_details"),
                    disabled: false,
                    items: [
                        { label: "Yes", value: true },
                        { label: "No", value: false },
                    ],
                }),
                new FilterFormControl({
                    name: "contract_comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("contract_comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Аnnuity]: [
                new FilterFormControl({
                    name: "rent_type",
                    type: FilterTypeEnum.Select,
                    label: "Rent type",
                    allowClear: true,
                    value: this.getFilterValue("rent_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "specific_rent_type",
                    type: FilterTypeEnum.Select,
                    label: "Specific rent type",
                    allowClear: true,
                    value: this.getFilterValue("specific_rent_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "rent_kind_type",
                    type: FilterTypeEnum.Select,
                    label: "Kind type",
                    allowClear: true,
                    value: this.getFilterValue("rent_kind_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Contragent]: [
                new FilterFormControl({
                    name: "contragent_type",
                    type: FilterTypeEnum.Select,
                    label: "Contragent type",
                    allowClear: true,
                    value: this.getFilterValue("contragent_type"),
                    disabled: false,
                    multiple: true,
                    items: [],
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contract_signer",
                    type: FilterTypeEnum.Select,
                    label: "Contract signer",
                    allowClear: true,
                    value: this.getFilterValue("contract_signer"),
                    disabled: false,
                    multiple: true,
                    items: [],
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contragent_phone",
                    type: FilterTypeEnum.Select,
                    label: "Phone",
                    allowClear: true,
                    value: this.getFilterValue("contragent_phone"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contragent_comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("contragent_comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
        };
    }

    private getSubleasesFilterControls(): {
        [section: string]: FilterFormControl[];
    } {
        return {
            [DocumentFilterSectionEnum.Popular]: [
                new FilterFormControl({
                    name: "cnum",
                    type: FilterTypeEnum.Select,
                    label: "Document number",
                    allowClear: true,
                    value: this.getFilterValue("cnum"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "farming_years",
                    type: FilterTypeEnum.Select,
                    label: "Farming year",
                    allowClear: true,
                    value: this.getFilterValue("farming_years"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "tenant",
                    type: FilterTypeEnum.Select,
                    label: "Tenant",
                    allowClear: true,
                    value: this.getFilterValue("tenant"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "kad_ident",
                    type: FilterTypeEnum.Select,
                    label: "Identifier",
                    allowClear: true,
                    value: this.getFilterValue("kad_ident"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Field]: [
                new FilterFormControl({
                    name: "virtual_ekatte_name",
                    type: FilterTypeEnum.Select,
                    label: "EKATTE",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ekatte_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "masiv",
                    type: FilterTypeEnum.Select,
                    label: "Masiv",
                    allowClear: true,
                    value: this.getFilterValue("masiv"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "number",
                    type: FilterTypeEnum.Select,
                    label: "Estate",
                    allowClear: true,
                    value: this.getFilterValue("number"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_category_title",
                    type: FilterTypeEnum.Select,
                    label: "Category",
                    allowClear: true,
                    value: this.getFilterValue("virtual_category_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_ntp_title",
                    type: FilterTypeEnum.Select,
                    label: "NTP",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ntp_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "mestnost",
                    type: FilterTypeEnum.Select,
                    label: "Local area",
                    allowClear: true,
                    value: this.getFilterValue("mestnost"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "allowable_type",
                    type: FilterTypeEnum.Select,
                    label: "Allowable type",
                    allowClear: true,
                    value: this.getFilterValue("allowable_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "irrigated_area",
                    type: FilterTypeEnum.Select,
                    label: "Irrigated area",
                    allowClear: true,
                    value: this.getFilterValue("irrigated_area"),
                    disabled: false,
                    items: [
                        { label: "Yes", value: true },
                        { label: "No", value: false },
                    ],
                    multiple: true,
                }),
                new FilterFormControl({
                    name: "comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Document]: [
                new FilterFormControl({
                    name: "virtual_contract_type",
                    type: FilterTypeEnum.Select,
                    label: "Contract type",
                    allowClear: true,
                    value: this.getFilterValue("virtual_contract_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "farming_name",
                    type: FilterTypeEnum.Select,
                    label: "Farming",
                    allowClear: true,
                    value: this.getFilterValue("farming_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contract_status_text",
                    type: FilterTypeEnum.Select,
                    label: "Status",
                    allowClear: true,
                    value: this.getFilterValue("contract_status_text"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contract_date_from",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract from date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_from"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "contract_date_to",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract To date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_to"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "contract_comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("contract_comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Аnnuity]: [
                new FilterFormControl({
                    name: "rent_kind_type",
                    type: FilterTypeEnum.Select,
                    label: "Kind type",
                    allowClear: true,
                    value: this.getFilterValue("rent_kind_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
        };
    }

    private getOwnershipFilterControls(): {
        [section: string]: FilterFormControl[];
    } {
        return {
            [DocumentFilterSectionEnum.Popular]: [
                new FilterFormControl({
                    name: "cnum",
                    type: FilterTypeEnum.Select,
                    label: "Document number",
                    allowClear: true,
                    value: this.getFilterValue("cnum"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "farming_name",
                    type: FilterTypeEnum.Select,
                    label: "Farming",
                    allowClear: true,
                    value: this.getFilterValue("farming_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "kad_ident",
                    type: FilterTypeEnum.Select,
                    label: "Identifier",
                    allowClear: true,
                    value: this.getFilterValue("kad_ident"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Field]: [
                new FilterFormControl({
                    name: "virtual_ekatte_name",
                    type: FilterTypeEnum.Select,
                    label: "EKATTE",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ekatte_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "masiv",
                    type: FilterTypeEnum.Select,
                    label: "Masiv",
                    allowClear: true,
                    value: this.getFilterValue("masiv"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "number",
                    type: FilterTypeEnum.Select,
                    label: "Estate",
                    allowClear: true,
                    value: this.getFilterValue("number"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_category_title",
                    type: FilterTypeEnum.Select,
                    label: "Category",
                    allowClear: true,
                    value: this.getFilterValue("virtual_category_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_ntp_title",
                    type: FilterTypeEnum.Select,
                    label: "NTP",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ntp_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "mestnost",
                    type: FilterTypeEnum.Select,
                    label: "Local area",
                    allowClear: true,
                    value: this.getFilterValue("mestnost"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "allowable_type",
                    type: FilterTypeEnum.Select,
                    label: "Allowable type",
                    allowClear: true,
                    value: this.getFilterValue("allowable_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "irrigated_area",
                    type: FilterTypeEnum.Select,
                    label: "Irrigated area",
                    allowClear: true,
                    value: this.getFilterValue("irrigated_area"),
                    disabled: false,
                    items: [
                        { label: "Yes", value: true },
                        { label: "No", value: false },
                    ],
                    multiple: true,
                }),
                new FilterFormControl({
                    name: "participation",
                    type: FilterTypeEnum.Select,
                    label: "Participation art.37",
                    allowClear: true,
                    value: this.getFilterValue("participation"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Document]: [
                new FilterFormControl({
                    name: "contract_date_from",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract from date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_from"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "contract_date_to",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract To date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_to"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "contract_comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("contract_comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
        };
    }

    private getMortgageFilterControls(): {
        [section: string]: FilterFormControl[];
    } {
        return {
            [DocumentFilterSectionEnum.Popular]: [
                new FilterFormControl({
                    name: "cnum",
                    type: FilterTypeEnum.Select,
                    label: "Document number",
                    allowClear: true,
                    value: this.getFilterValue("cnum"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "creditor",
                    type: FilterTypeEnum.Select,
                    label: "Creditor",
                    allowClear: true,
                    value: this.getFilterValue("creditor"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "kad_ident",
                    type: FilterTypeEnum.Select,
                    label: "Identifier",
                    allowClear: true,
                    value: this.getFilterValue("kad_ident"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Field]: [
                new FilterFormControl({
                    name: "virtual_ekatte_name",
                    type: FilterTypeEnum.Select,
                    label: "EKATTE",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ekatte_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "masiv",
                    type: FilterTypeEnum.Select,
                    label: "Masiv",
                    allowClear: true,
                    value: this.getFilterValue("masiv"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "number",
                    type: FilterTypeEnum.Select,
                    label: "Estate",
                    allowClear: true,
                    value: this.getFilterValue("number"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_category_title",
                    type: FilterTypeEnum.Select,
                    label: "Category",
                    allowClear: true,
                    value: this.getFilterValue("virtual_category_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_ntp_title",
                    type: FilterTypeEnum.Select,
                    label: "NTP",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ntp_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "mestnost",
                    type: FilterTypeEnum.Select,
                    label: "Local area",
                    allowClear: true,
                    value: this.getFilterValue("mestnost"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "allowable_type",
                    type: FilterTypeEnum.Select,
                    label: "Allowable type",
                    allowClear: true,
                    value: this.getFilterValue("allowable_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "irrigated_area",
                    type: FilterTypeEnum.Select,
                    label: "Irrigated area",
                    allowClear: true,
                    value: this.getFilterValue("irrigated_area"),
                    disabled: false,
                    items: [
                        { label: "Yes", value: true },
                        { label: "No", value: false },
                    ],
                    multiple: true,
                }),
                new FilterFormControl({
                    name: "comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Document]: [
                new FilterFormControl({
                    name: "farming_name",
                    type: FilterTypeEnum.Select,
                    label: "Farming",
                    allowClear: true,
                    value: this.getFilterValue("farming_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contract_date_from",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract from date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_from"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "contract_date_to",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract To date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_to"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "start_date",
                    type: FilterTypeEnum.Date,
                    label: "Contract validity start date",
                    allowClear: true,
                    value: this.getFilterValue("start_date"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "due_date",
                    type: FilterTypeEnum.Date,
                    label: "Contract validity due date",
                    allowClear: true,
                    value: this.getFilterValue("due_date"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "hypothecs_due_date",
                    type: FilterTypeEnum.Date,
                    label: "Maturity date",
                    allowClear: true,
                    value: this.getFilterValue("hypothecs_due_date"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "contract_comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("contract_comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
        };
    }

    private getSalesFilterControls(): {
        [section: string]: FilterFormControl[];
    } {
        return {
            [DocumentFilterSectionEnum.Popular]: [
                new FilterFormControl({
                    name: "cnum",
                    type: FilterTypeEnum.Select,
                    label: "Document number",
                    allowClear: true,
                    value: this.getFilterValue("cnum"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "buyer",
                    type: FilterTypeEnum.Select,
                    label: "Buyer",
                    allowClear: true,
                    value: this.getFilterValue("buyer"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "kad_ident",
                    type: FilterTypeEnum.Select,
                    label: "Identifier",
                    allowClear: true,
                    value: this.getFilterValue("kad_ident"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Field]: [
                new FilterFormControl({
                    name: "virtual_ekatte_name",
                    type: FilterTypeEnum.Select,
                    label: "EKATTE",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ekatte_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "masiv",
                    type: FilterTypeEnum.Select,
                    label: "Masiv",
                    allowClear: true,
                    value: this.getFilterValue("masiv"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "number",
                    type: FilterTypeEnum.Select,
                    label: "Estate",
                    allowClear: true,
                    value: this.getFilterValue("number"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_category_title",
                    type: FilterTypeEnum.Select,
                    label: "Category",
                    allowClear: true,
                    value: this.getFilterValue("virtual_category_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "virtual_ntp_title",
                    type: FilterTypeEnum.Select,
                    label: "NTP",
                    allowClear: true,
                    value: this.getFilterValue("virtual_ntp_title"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "mestnost",
                    type: FilterTypeEnum.Select,
                    label: "Local area",
                    allowClear: true,
                    value: this.getFilterValue("mestnost"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "allowable_type",
                    type: FilterTypeEnum.Select,
                    label: "Allowable type",
                    allowClear: true,
                    value: this.getFilterValue("allowable_type"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "irrigated_area",
                    type: FilterTypeEnum.Select,
                    label: "Irrigated area",
                    allowClear: true,
                    value: this.getFilterValue("irrigated_area"),
                    disabled: false,
                    items: [
                        { label: "Yes", value: true },
                        { label: "No", value: false },
                    ],
                    multiple: true,
                }),
                new FilterFormControl({
                    name: "comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
            [DocumentFilterSectionEnum.Document]: [
                new FilterFormControl({
                    name: "farming_name",
                    type: FilterTypeEnum.Select,
                    label: "Farming",
                    allowClear: true,
                    value: this.getFilterValue("farming_name"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
                new FilterFormControl({
                    name: "contract_date_from",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract from date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_from"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "contract_date_to",
                    type: FilterTypeEnum.Date,
                    label: "Conclusion of contract To date",
                    allowClear: true,
                    value: this.getFilterValue("contract_date_to"),
                    disabled: false,
                }),
                new FilterFormControl({
                    name: "contract_comment",
                    type: FilterTypeEnum.Select,
                    label: "Note",
                    allowClear: true,
                    value: this.getFilterValue("contract_comment"),
                    disabled: false,
                    items: [],
                    multiple: true,
                    loadItems: this.loadItemsFn,
                }),
            ],
        };
    }
}
