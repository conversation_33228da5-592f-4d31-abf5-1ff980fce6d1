import { Component, computed, inject, input } from "@angular/core";
import { Router } from "@angular/router";
import { DocumentTypeEnum } from "@geoscan/main-lib/types";

@Component({
    selector: "gs-documents-attribute-toolbar",
    templateUrl: "./documents-attribute-toolbar.component.html",
    styleUrl: "./documents-attribute-toolbar.component.scss",
})
export class DocumentsAttributeToolbarComponent {
    totalCount = input<number>(0);
    documentType = input<DocumentTypeEnum>();

    documentTypeEnum = DocumentTypeEnum;

    shouldShowRenewalButton = computed(() => {
        const docType = this.documentType();
        return (
            docType === DocumentTypeEnum.Contracts ||
            docType === DocumentTypeEnum.Subleases
        );
    });

    private router = inject(Router);

    onRenewalClick(): void {
        // Navigate to renewal page based on document type
        const documentType = this.documentType();
        if (documentType === DocumentTypeEnum.Contracts) {
            this.router.navigate(["/client/documents/contracts/renewal"]);
        } else if (documentType === DocumentTypeEnum.Subleases) {
            this.router.navigate(["/client/documents/subleases/renewal"]);
        }
    }
}
