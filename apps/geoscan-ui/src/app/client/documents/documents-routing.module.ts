import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { DocumentTypeEnum } from "@geoscan/main-lib/types";
import { ClientGuard } from "../../shared/guards";
import { ContractPageWrapperComponent } from "./contract-page-wrapper/contract-page-wrapper.component";
import { DocumentsComponent } from "./documents.component";

const routes: Routes = [
    {
        path: "",
        component: DocumentsComponent,
        canActivate: [ClientGuard],
        children: [
            {
                path: "",
                pathMatch: "full",
                redirectTo: DocumentTypeEnum.Contracts,
            },
            {
                path: DocumentTypeEnum.Contracts,
                loadChildren: () =>
                    import("./contracts-list/contracts-list.module").then(
                        (m) => m.ContractsListModule
                    ),
                data: {
                    documentType: DocumentTypeEnum.Contracts,
                },
            },

            {
                path: DocumentTypeEnum.Ownership,
                loadChildren: () =>
                    import("./ownership-list/ownership-list.module").then(
                        (m) => m.OwnershipListModule
                    ),
                data: {
                    documentType: DocumentTypeEnum.Ownership,
                },
            },
            {
                path: DocumentTypeEnum.Subleases,
                loadChildren: () =>
                    import("./subleases-list/subleases-list.module").then(
                        (m) => m.SubleasesListModule
                    ),
                data: {
                    documentType: DocumentTypeEnum.Subleases,
                },
            },
        ],
    },
    {
        path: `${DocumentTypeEnum.Contracts}/renewal`,
        loadChildren: () =>
            import("./contracts-renewal/contracts-renewal.module").then(
                (m) => m.ContractsRenewalModule
            ),
        data: {
            documentType: DocumentTypeEnum.Contracts,
        },
    },
    {
        path: `${DocumentTypeEnum.Subleases}/renewal`,
        loadChildren: () =>
            import("./contracts-renewal/contracts-renewal.module").then(
                (m) => m.ContractsRenewalModule
            ),
        data: {
            documentType: DocumentTypeEnum.Subleases,
        },
    },
    {
        path: `:type/:subType/:id`,
        component: ContractPageWrapperComponent,
    },
    {
        path: `:type/:id`,
        component: ContractPageWrapperComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class DocumentsRoutingModule {}
