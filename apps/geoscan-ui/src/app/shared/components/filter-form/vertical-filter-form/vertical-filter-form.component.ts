import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
    output,
} from "@angular/core";
import { FormGroup } from "@angular/forms";
import {
    FilterFormControl,
    FilterFormControlValueType,
    FilterTypeEnum,
} from "@geoscan/main-lib/types";
import { flatMap } from "lodash";
import { NzSizeLDSType } from "ng-zorro-antd/core/types";

@Component({
    selector: "gs-vertical-filter-form",
    templateUrl: "./vertical-filter-form.component.html",
    styleUrls: ["./vertical-filter-form.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VerticalFilterFormComponent {
    activeSection = input<string>(null);
    filterControlsBySection = input<{
        [section: string]: FilterFormControl[];
    }>({});
    size = input<NzSizeLDSType>("default");
    formInit = output<FormGroup>();
    formValueChange = output<{ [key: string]: FilterFormControlValueType }>();

    filterSections = computed(() =>
        Object.keys(this.filterControlsBySection())
    );
    filterForm = computed<FormGroup>(() =>
        this.generateForm(this.filterControlsBySection())
    );
    visibleFilterControlsBySection = computed(() =>
        Object.entries(this.filterControlsBySection()).reduce(
            (acc, [section, controls]) => {
                acc[section] = controls.filter(
                    (control) =>
                        control.getOptions().type !== FilterTypeEnum.Hidden
                );
                return acc;
            },
            {} as { [section: string]: FilterFormControl[] }
        )
    );

    generateForm(filterControlsBySection: {
        [section: string]: FilterFormControl[];
    }): FormGroup {
        const controls = flatMap(filterControlsBySection ?? {}).reduce(
            (acc, control) => {
                const controlName = control.getOptions().name;
                return {
                    ...acc,
                    [controlName]: control,
                };
            },
            {}
        );

        const form = new FormGroup(controls);
        this.formInit.emit(form);
        return form;
    }
}
