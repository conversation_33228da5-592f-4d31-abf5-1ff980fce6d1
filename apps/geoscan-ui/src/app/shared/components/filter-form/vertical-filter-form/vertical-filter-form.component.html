<form
    nz-form
    nzLayout="vertical"
    [formGroup]="filterForm()"
    class="client-form filter h-100"
>
    <nz-collapse [nzBordered]="false">
        <ng-template #expandedIconTpl>
            <span class="gs-icon-arrow"></span>
        </ng-template>

        @for (section of filterSections(); track section) {
            <nz-collapse-panel
                [nzHeader]="section | translate"
                [nzExpandedIcon]="expandedIconTpl"
                [nzActive]="section === activeSection()"
            >
                @for (
                    filterControl of visibleFilterControlsBySection()[section];
                    track filterControl.uuid
                ) {
                    <gs-filter-form-item
                        [formControl]="filterControl"
                        [formLayout]="'vertical'"
                        [size]="size()"
                    ></gs-filter-form-item>
                }
            </nz-collapse-panel>
        }
    </nz-collapse>
</form>
