import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    inject,
    Injector,
    input,
    OnInit,
    signal,
} from "@angular/core";
import { takeUntilDestroyed, toObservable } from "@angular/core/rxjs-interop";
import {
    ControlValueAccessor,
    FormArray,
    FormGroup,
    NG_VALUE_ACCESSOR,
    NgControl,
    ValidationErrors,
} from "@angular/forms";
import {
    FilterFormControl,
    FilterFormControlOptionsType,
    FilterFormControlValueType,
    FilterTypeEnum,
    SelectFilterControlOptionsType,
} from "@geoscan/main-lib/types";
import { isEqual } from "lodash";
import { NzSizeLDSType } from "ng-zorro-antd/core/types";
import { NzFormLayoutType } from "ng-zorro-antd/form";
import { debounceTime, distinctUntilChanged, filter, map } from "rxjs";

@Component({
    selector: "gs-filter-form-item",
    templateUrl: "./filter-form-item.component.html",
    styleUrls: ["./filter-form-item.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: FilterFormItemComponent,
        },
    ],
})
export class FilterFormItemComponent implements OnInit, ControlValueAccessor {
    filterTypeEnum = FilterTypeEnum;

    size = input<NzSizeLDSType>("default");
    formLayout = input<NzFormLayoutType>("vertical");
    options = signal<FilterFormControlOptionsType>(null);
    errors = signal<ValidationErrors>(null);
    disabled = signal(false);
    touched = signal(false);
    value = signal<FilterFormControlValueType>(null);

    controlName = computed(() => this.options()?.name);
    status = computed(() => (this.errors() ? "error" : undefined));
    type = computed(() => this.options()?.type);
    label = computed(() => this.options()?.label);
    info = computed(() => this.options()?.info);
    allowClear = computed(() => this.options()?.allowClear);

    // Properties for Select type
    search = signal<string>(null);
    debouncedSearch$ = toObservable(this.search);
    loading = signal<boolean>(false);
    dropdownOpen = signal<boolean>(false);
    totalItemsCount = signal<number>(0);
    isSelectFormControl = computed(() => this.type() === FilterTypeEnum.Select);
    loadedItemsCount = computed(
        () => (this.options() as SelectFilterControlOptionsType)?.items?.length
    );
    loadItemsFromServer = computed(
        () => !!(this.options() as SelectFilterControlOptionsType)?.loadItems
    );
    showLoadMoreButton = computed(() => {
        if (!this.loadItemsFromServer() || this.loading()) {
            return false;
        }

        return this.loadedItemsCount() < this.totalItemsCount();
    });
    itemsLastLoadedPage: number = null;
    readonly itemsPerPage = 10;

    // ControlValueAccessor default methods
    onChange = (value: FilterFormControlValueType) => {};
    onTouched = () => {};
    onValidatorChange = () => {};

    private injector = inject(Injector);
    private destroyRef = inject(DestroyRef);
    private ngControl: NgControl;
    private parent: FormGroup | FormArray | null;
    private parentValue: { [key: string]: FilterFormControlValueType } | null;

    ngOnInit(): void {
        this.ngControl = this.injector.get(NgControl, null);

        this.initializeParent();
        this.initializeOptions();
        this.initializeSearch();
    }

    onClear(event: PointerEvent) {
        // Prevent the click event from propagating to prevent the actual clear control emitting null value
        event.stopPropagation();

        // Clear the value
        this.onValueChanged(undefined);
    }

    initializeParent() {
        this.parent = this.ngControl?.control?.parent;

        if (!this.parent) {
            const controlName = this.ngControl?.name;
            console.error(
                `The '${controlName}' control  is not part of any form`
            );
            return;
        }

        // Set initial value from the parent form
        this.parentValue = this.parent.getRawValue() ?? {};

        // Subscribe to value changes of the parent form and check if the current control is of type Select and needs to reload its items
        this.parent.valueChanges
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                filter(
                    () =>
                        this.isSelectFormControl() && this.loadItemsFromServer()
                    // If the control is not of type Select or the loadItems callback is not provided,
                    // we don't need to check for reloading items
                )
            )
            .subscribe({
                next: (value) => {
                    // Get the updated controls from the parent form
                    const updatedControls = Object.keys(value).filter(
                        (key) =>
                            JSON.stringify(this.parentValue[key]) !==
                            JSON.stringify(value[key])
                    );

                    // If the value of any control different than the current has changed, we need to reload current control's items on the next dropdown open
                    if (
                        updatedControls.length > 0 &&
                        !updatedControls.includes(this.controlName())
                    ) {
                        this.itemsLastLoadedPage = null; // Set the itemsLastLoadedPage to null to indicate that we need to reload the items on the next dropdown open
                    }

                    // If valueChanges emits the same value as the current value (force emit event), reload the items on the next dropdown open
                    if (isEqual(this.parentValue, value)) {
                        this.itemsLastLoadedPage = null;
                    }

                    this.parentValue = value; // Update the parent value to the new value
                },
            });
    }

    initializeOptions() {
        const options = (
            this.ngControl?.control as FilterFormControl
        )?.getOptions();

        if (!options) {
            console.error("FilterFormControl invalid options");
            return;
        }

        this.options.set(options);
    }

    initializeSearch() {
        if (this.type() !== FilterTypeEnum.Select) {
            //  The search functionality is supported only for Select type
            return;
        }

        if (!this.loadItemsFromServer()) {
            return;
        }

        this.debouncedSearch$
            .pipe(
                filter(() => this.dropdownOpen()),
                map((value) => value?.trim()),
                distinctUntilChanged(),
                debounceTime(300), // Debounce the search input to avoid too many requests
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe({
                next: () => {
                    // Search value is changed
                    this.itemsLastLoadedPage = null; // Set the itemsLastLoadedPage to null to indicate that we need to reload the items on the next dropdown open
                    const pageToLoad = 1; // Reset to the first page
                    this.loadItems(pageToLoad);
                },
            });
    }

    onValueChanged(newValue: FilterFormControlValueType) {
        // This method is called when the value is updated through the UI (by the user).

        this.onChange(newValue); // Notify the forms API of value changes, so the form control's value is updated
        this.value.set(newValue); // Update the local model
        this.addValueToItemsListIfNotPresent(); // Ensure the current value is added to the items list if not present
        this.errors.set(this.ngControl?.errors ?? null); // Set errors if any

        this.markAsTouched();
    }

    writeValue(newValue: FilterFormControlValueType) {
        // This method is called by the Angular forms API when the value is updated programmatically through the form control.

        this.ngControl?.control?.updateValueAndValidity({ emitEvent: false }); // Update the form control's value and validity
        this.value.set(newValue); // Update the local model
        this.addValueToItemsListIfNotPresent(); // Ensure the current value is added to the items list if not present
        this.errors.set(this.ngControl?.errors ?? null); // Set errors if any
    }

    setDisabledState(isDisabled: boolean) {
        this.disabled.set(isDisabled);
    }

    registerOnChange(onChange: (value: FilterFormControlValueType) => void) {
        this.onChange = onChange;
    }

    registerOnTouched(onTouched: () => void) {
        this.onTouched = onTouched;
    }

    onDropdownOpenChange(isOpen: boolean) {
        this.dropdownOpen.set(isOpen);

        if (!isOpen) {
            return; // If the dropdown is closed, we don't need to load items
        }

        if (!this.loadItemsFromServer()) {
            return;
        }

        // Load items on dropdown open
        // If the itemsLastLoadedPage is null, load the first page of items, otherwise show the already loaded items list without reloading
        const pageToLoad = this.itemsLastLoadedPage ?? 1;
        this.loadItems(pageToLoad);
    }

    onLoadMore() {
        if (!this.loadItemsFromServer()) {
            return;
        }

        // Load items on "Load more" button click. Pass the next page number to load or the first page if no items were loaded yet
        const pageToLoad = this.itemsLastLoadedPage
            ? this.itemsLastLoadedPage + 1
            : 1;

        this.loadItems(pageToLoad);
    }

    loadItems(pageToLoad: number) {
        if (this.type() !== FilterTypeEnum.Select) {
            // The loadItems() callback is supported only for Select type
            return;
        }

        if (!pageToLoad || pageToLoad < 1) {
            console.error(
                `Invalid page number: ${pageToLoad}. Page number must be greater than 0.`
            );
            return;
        }

        if (pageToLoad === this.itemsLastLoadedPage) {
            // Show currently loaded items without reloading
            return;
        }

        // Load items from the server
        const options = this.options() as SelectFilterControlOptionsType;

        if (!options) {
            console.error("FilterFormControl options are not set");
            return;
        }

        this.loading.set(true);
        options
            .loadItems(
                this.controlName(),
                pageToLoad,
                this.itemsPerPage,
                this.search()
            )
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
                next: (response) => {
                    this.loading.set(false);

                    this.options.update(
                        (prevOptions: SelectFilterControlOptionsType) => {
                            // Get previously loaded items that are not present in the current response
                            const prevItems = prevOptions.items.filter(
                                (prevItem) =>
                                    !response.rows.find(
                                        (row) => row.value === prevItem.value
                                    )
                            );

                            const items =
                                pageToLoad === 1
                                    ? response.rows
                                    : [...prevItems, ...response.rows];

                            return {
                                ...prevOptions,
                                items,
                            } as SelectFilterControlOptionsType;
                        }
                    );
                    this.addValueToItemsListIfNotPresent(); // Ensure the current value is added to the items list if not present
                    this.totalItemsCount.set(response.total);
                    this.itemsLastLoadedPage = pageToLoad; // Update the last loaded page number
                },
                error: () => {
                    this.loading.set(false);
                },
            });
    }

    onSearch(value: string) {
        this.search.set(value);
    }

    markAsTouched() {
        if (!this.touched()) {
            this.onTouched();
            this.touched.set(true);
        }
    }

    /**
     * This method is used to add the current value of the control to the items list if it is not already present. (Select control type only)
     *
     * It fixes the empty select box when the value is set programmatically and is missing from the items list.
     *
     * @returns
     */
    private addValueToItemsListIfNotPresent() {
        if (!this.isSelectFormControl() || this.value() === undefined) {
            return;
        }

        const options = this.options() as SelectFilterControlOptionsType;
        const items = options?.items ?? [];
        const isMultiple = options?.multiple ?? false;

        // If the value is not present in the items, we need to add it to the items list

        if (Array.isArray(this.value()) && isMultiple) {
            // If the this.value() is an array, we need to check if all the elements in this.value() are present in the items list
            // If not, we need to add them to the items list
            const missingItems = (this.value() as (string | number)[]).filter(
                (value) => !items.find((item) => item.value === value)
            );

            if (missingItems.length > 0) {
                // Add missing items to the items list
                const newItems = missingItems.map((value) => ({
                    label: value?.toString(),
                    value: value as string | number,
                }));

                this.options.update(
                    (prevOptions: SelectFilterControlOptionsType) => {
                        return {
                            ...prevOptions,
                            items: [...prevOptions.items, ...newItems],
                        };
                    }
                );
            }

            return;
        }

        // If the this.value() is not an array, we need to check if it is present in the items list

        if (!items.find((item) => item.value === this.value())) {
            // If the value is not present in the items list, we need to add it
            this.options.update(
                (prevOptions: SelectFilterControlOptionsType) => {
                    const newItem = {
                        label: this.value()?.toString(),
                        value: this.value() as string | number,
                    };

                    return {
                        ...prevOptions,
                        items: [...prevOptions.items, newItem],
                    };
                }
            );
        }
    }
}
