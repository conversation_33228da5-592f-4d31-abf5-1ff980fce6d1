<form
    nz-form
    nzLayout="horizontal"
    [formGroup]="filterForm()"
    class="client-form filter horizontal h-100"
>
    <div nz-row nzAlign="middle" [nzGutter]="12">
        @for (
            filterControl of visibleFilterControls();
            track filterControl.getOptions().name
        ) {
            <div nz-col [nzSpan]="24 / visibleFilterControls().length">
                <gs-filter-form-item
                    [formControl]="filterControl"
                    [formLayout]="'horizontal'"
                    [size]="size()"
                ></gs-filter-form-item>
            </div>
        }
    </div>
</form>
