import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
    output,
} from "@angular/core";
import { FormGroup } from "@angular/forms";
import {
    FilterFormControl,
    FilterFormControlValueType,
    FilterTypeEnum,
    FormModeEnum,
} from "@geoscan/main-lib/types";
import { NzSizeLDSType } from "ng-zorro-antd/core/types";

@Component({
    selector: "gs-horizontal-filter-form",
    templateUrl: "./horizontal-filter-form.component.html",
    styleUrls: ["./horizontal-filter-form.component.scss"],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HorizontalFilterFormComponent {
    activeSection = input<string>(null);
    filterControls = input<FilterFormControl[]>([]);
    size = input<NzSizeLDSType>("default");
    formInit = output<FormGroup>();
    formValueChange = output<{ [key: string]: FilterFormControlValueType }>();

    filterForm = computed<FormGroup>(() =>
        this.generateForm(this.filterControls())
    );
    visibleFilterControls = computed(() =>
        this.filterControls().filter(
            (control) => control.getOptions().type !== FilterTypeEnum.Hidden
        )
    );

    formModeEnum = FormModeEnum;
    filterTypeEnum = FilterTypeEnum;

    generateForm(filterControls: FilterFormControl[]): FormGroup {
        const controls = filterControls.reduce((acc, control) => {
            const controlName = control.getOptions().name;
            return {
                ...acc,
                [controlName]: control,
            };
        }, {});

        const form = new FormGroup(controls);
        this.formInit.emit(form);
        return form;
    }
}
