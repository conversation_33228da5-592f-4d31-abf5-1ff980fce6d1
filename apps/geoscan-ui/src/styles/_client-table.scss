@use "./rem-calc";

.client-table {
    table {
        border-radius: 0;
    }

    .ant-table-thead {
        tr {
            th {
                border-radius: 0 !important;
                padding: 0 var(--padding-sm);
                font: {
                    size: var(--font-sm);
                    weight: normal;
                }
                color: var(--table-header-text-color);
                height: rem-calc.rem-calc(30);

                .ant-table-column-sorter {
                    color: var(--table-column-sorter-icon-bg-color);
                }
            }

            &.header-md th {
                padding: var(--padding-sm);
            }
        }
    }

    .ant-table-tbody {
        tr {
            &.expanded-row-child {
                td {
                    background-color: var(--table-expanded-row-bg-color);
                }
            }

            &.ant-table-measure-now {
                visibility: collapse !important;
            }

            td {
                padding: var(--padding-sm);
                min-height: rem-calc.rem-calc(48);
                font: {
                    size: var(--font-md);
                    family: var(--font-normal);
                }
                line-height: normal;
                white-space: inherit;

                &.ant-table-cell-fix-left,
                &.ant-table-cell-fix-right {
                    z-index: 4;
                }

                @media screen and (max-width: 1600px) {
                    min-height: rem-calc.rem-calc(36);
                }

                .ant-typography {
                    margin-bottom: 0;
                }

                .highlighted-text {
                    color: var(--table-text-highlighted-color);
                }

                .anticon:not(.anticon-close-circle) {
                    color: var(--icon-primary-color);
                    font-size: var(--font-xxl);
                    cursor: pointer;

                    &.anticon-down,
                    &.anticon-right {
                        font-size: var(--font-sm);
                    }
                }

                .icons-wrapper {
                    position: relative;
                    display: inline-flex;
                    align-items: center;

                    .gs-btn-icon {
                        width: rem-calc.rem-calc(24);
                        height: rem-calc.rem-calc(24);

                        @media screen and (max-width: 1600px) {
                            width: rem-calc.rem-calc(18);
                            height: rem-calc.rem-calc(18);
                        }
                    }
                }

                .icon-wrapper,
                .ant-btn {
                    &:hover {
                        .anticon {
                            color: var(--icon-hover-color);
                        }

                        [class*="gs-icon"] {
                            background-color: var(--icon-hover-color);
                        }

                        .icon-disabled {
                            &.anticon {
                                color: var(--icon-disabled-color);
                            }

                            &[class*="gs-icon"] {
                                background-color: var(--icon-disabled-color);
                            }
                        }
                    }

                    &.icon-selected {
                        [class*="gs-icon"] {
                            background-color: var(--icon-brand-color-primary);
                        }
                    }
                }

                .anticon-double-right {
                    font-size: var(--font-xs);
                }

                .ant-btn[ant-click-animating-without-extra-node="true"]::after {
                    display: none;
                }
            }

            &.selected-row td {
                background: var(--table-selected-row-bg-color);
                color: var(--table-selected-row-text-color);
            }

            &.highlighted-row td {
                background-color: var(--table-highlighted-row-bg-color);
            }

            .wrapper-with-divider {
                display: inline-block;

                nz-divider {
                    margin: rem-calc.rem-calc(4) 0;
                }
            }

            &.ant-table-expanded-row {
                font-size: var(--font-sm);

                .expanded-row {
                    &-label {
                        font-family: var(--font-light);
                        color: var(--text-highlighted-brand-color-primary);
                    }

                    &-value {
                        margin-top: rem-calc.rem-calc(6);
                        font-family: var(--font-medium);
                    }
                }
            }

            &.load-more {
                td {
                    background-color: transparent;
                }
            }
        }
    }

    .ant-table-bordered {
        .ant-table-container {
            border-left: none;
        }

        .ant-table-thead > tr > th:first-child {
            border-right: none !important;
        }

        .ant-table-tbody tr td {
            white-space: nowrap;
            border-left: none !important;
            border-right: none !important;
            border-top: none !important;
        }
    }
}
