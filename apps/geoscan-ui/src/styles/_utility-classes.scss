@use "./rem-calc";

$marginSizes: 0, xs, sm, md, lg;
$paddingSizes: 0, xs, sm, md, lg;
$lineHeights: xs, sm, md, lg, xl, xxl;
$lineHeightHeadings: h1, h2, h3, h4, h5;
$fontSizes: xs, sm, md, lg, xl, xxl;
$fontFamilies: light, normal, medium, "semi-bold", bold, italic;
$gaps: 0, xs, sm, md, lg, xl, xxl;

// Margin
@each $size in $marginSizes {
    .m-#{$size} {
        margin: if($size == 0, 0, var(--margin-#{$size}));
    }

    .mt-#{$size} {
        margin-top: if($size == 0, 0, var(--margin-#{$size}));
    }

    .mb-#{$size} {
        margin-bottom: if($size == 0, 0, var(--margin-#{$size}));
    }

    .ml-#{$size} {
        margin-left: if($size == 0, 0, var(--margin-#{$size}));
    }

    .mr-#{$size} {
        margin-right: if($size == 0, 0, var(--margin-#{$size}));
    }

    .mx-#{$size} {
        margin-left: if($size == 0, 0, var(--margin-#{$size}));
        margin-right: if($size == 0, 0, var(--margin-#{$size}));
    }

    .my-#{$size} {
        margin-top: if($size == 0, 0, var(--margin-#{$size}));
        margin-bottom: if($size == 0, 0, var(--margin-#{$size}));
    }
}

.m-auto {
    margin: auto !important;
}

.mt-auto {
    margin-top: auto !important;
}

.mb-auto {
    margin-bottom: auto !important;
}

.ml-auto {
    margin-left: auto;
}

.mr-auto {
    margin-right: auto;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.my-auto {
    margin-top: auto;
    margin-bottom: auto;
}

// Padding
@each $size in $paddingSizes {
    .p-#{$size} {
        padding: if($size == 0, 0, var(--padding-#{$size}));
    }

    .pt-#{$size} {
        padding-top: if($size == 0, 0, var(--padding-#{$size}));
    }

    .pb-#{$size} {
        padding-bottom: if($size == 0, 0, var(--padding-#{$size}));
    }

    .pl-#{$size} {
        padding-left: if($size == 0, 0, var(--padding-#{$size}));
    }

    .pr-#{$size} {
        padding-right: if($size == 0, 0, var(--padding-#{$size}));
    }

    .px-#{$size} {
        padding-left: if($size == 0, 0, var(--padding-#{$size}));
        padding-right: if($size == 0, 0, var(--padding-#{$size}));
    }

    .py-#{$size} {
        padding-top: if($size == 0, 0, var(--padding-#{$size}));
        padding-bottom: if($size == 0, 0, var(--padding-#{$size}));
    }
}

// Line height
@each $size in $lineHeights {
    .l-h-#{$size} {
        line-height: var(--line-height-#{$size});
    }
}

// Line height for headings
@each $size in $lineHeightHeadings {
    .l-h-#{$size} {
        line-height: var(--line-height-#{$size});
    }
}

// Font size
@each $size in $fontSizes {
    .font-#{$size} {
        font-size: var(--font-#{$size});
    }
}

// Font family
@each $family in $fontFamilies {
    .font-#{$family} {
        font-family: var(--font-#{$family});
    }
}

@each $gap in $gaps {
    .gap-#{$gap} {
        gap: var(if($gap == 0, 0, --gap-#{$gap}));
    }
}

// Deprecated but still used in some places
.mt-4 {
    margin-top: rem-calc.rem-calc(4);
}

.mt-30 {
    margin-top: rem-calc.rem-calc(30);
}

.mt-32 {
    margin-top: rem-calc.rem-calc(32);
}

.mt-35 {
    margin-top: rem-calc.rem-calc(35);
}

.mt-40 {
    margin-top: rem-calc.rem-calc(40);
}

.mt-48 {
    margin-top: rem-calc.rem-calc(48);
}

.mr-4 {
    margin-right: rem-calc.rem-calc(4);
}

.mb-4 {
    margin-bottom: rem-calc.rem-calc(4) !important;
}

.mb-32 {
    margin-bottom: rem-calc.rem-calc(32);
}

.mb-44 {
    margin-bottom: rem-calc.rem-calc(44);
}

.mb-48 {
    margin-bottom: rem-calc.rem-calc(48);
}

.ml-4 {
    margin-left: 4px;
}

.ml-32 {
    margin-left: rem-calc.rem-calc(32);
}

.ml-200 {
    margin-left: rem-calc.rem-calc(200);
}

.p-8 {
    padding: rem-calc.rem-calc(8);
}

.p-20 {
    padding: rem-calc.rem-calc(20);
}

.p-60 {
    padding: rem-calc.rem-calc(60);
}

.pt-5 {
    padding-top: rem-calc.rem-calc(5);
}

.pt-30 {
    padding-top: rem-calc.rem-calc(30);
}

.pt-48 {
    padding-top: rem-calc.rem-calc(48);
}

.pr-4 {
    padding-right: rem-calc.rem-calc(4);
}

.pr-80 {
    padding-right: rem-calc.rem-calc(80);
}

.pl-4 {
    padding-left: rem-calc.rem-calc(4);
}

.pl-20 {
    padding-left: rem-calc.rem-calc(20);
}

.pl-30 {
    padding-left: rem-calc.rem-calc(30);
}

.pl-32 {
    padding-left: rem-calc.rem-calc(32);
}

.py-2 {
    padding-top: rem-calc.rem-calc(2);
    padding-bottom: rem-calc.rem-calc(2);
}

.py-6 {
    padding-top: rem-calc.rem-calc(6);
    padding-bottom: rem-calc.rem-calc(6);
}

.py-20 {
    padding-top: rem-calc.rem-calc(20);
    padding-bottom: rem-calc.rem-calc(20);
}

.py-32 {
    padding-top: rem-calc.rem-calc(32);
    padding-bottom: rem-calc.rem-calc(32);
}

.px-4 {
    padding-left: rem-calc.rem-calc(4);
    padding-right: rem-calc.rem-calc(4);
}

.px-50 {
    padding-left: rem-calc.rem-calc(50);
    padding-right: rem-calc.rem-calc(50);
}

/* Clearfix */
.clearfix {
    &:after {
        display: block;
        content: "";
        clear: both;
    }
}

/* Text */
.text-pre {
    white-space: pre-wrap;
}

.text-ellipsis {
    text-overflow: ellipsis;
}

.no-wrap {
    white-space: nowrap;
}

/* Text Alignment */
.align-center {
    text-align: center !important;
}

.align-right {
    text-align: right !important;
}

.align-left {
    text-align: left !important;
}

.align-end {
    text-align: end !important;
}

.align-justify {
    text-align: justify !important;
}

/* Float */
.float-left {
    float: left;
}

.float-right {
    float: right;
}

/* Cursor */
.cursor-pointer {
    cursor: pointer;
}

.cursor-no-pointer {
    cursor: default;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

/* Display */
.display-block {
    display: block;
}

.display-inline-block {
    display: inline-block;
}

.display-flex {
    display: flex;
}

.display-inline-flex {
    display: inline-flex;
}

.display-none {
    display: none;
}

.display-none-small {
    display: block;

    @media screen and (max-width: 1599px) {
        display: none;
    }
}

.display-none-medium {
    display: none;

    @media screen and (max-width: 1599px) {
        display: block;
    }
}

/* Flex */
.flex-direction-column {
    flex-direction: column;
}

.flex-direction-row {
    flex-direction: row !important;
}

.flex-align-flex-start {
    align-items: flex-start;
}

.flex-align-center {
    align-items: center;
}

.flex-align-flex-end {
    align-items: flex-end;
}

.flex-align-self-center {
    align-self: center;
}

.flex-align-self-baseline {
    align-self: baseline;
}

.flex-justify-content-left {
    justify-content: left;
}

.flex-justify-content-center {
    justify-content: center;
}

.flex-justify-content-right {
    justify-content: right;
}

.flex-justify-content-flex-end {
    justify-content: flex-end;
}

.flex-justify-content-space-around {
    justify-content: space-around;
}

.flex-justify-content-space-between {
    justify-content: space-between;
}

.flex-none {
    flex: none;
}

.flex-1 {
    flex: 1;
}

.flex-wrap-wrap {
    flex-wrap: wrap;
}

.flex-no-wrap {
    flex-wrap: nowrap;
}

.flex-auto {
    flex: auto !important;
}

/* Border */
.border-none {
    border: none;
}

.border-t-0 {
    border-top: none;
}

.border-r-0 {
    border-right: none;
}

.border-b-0 {
    border-bottom: none;
}

.border-l-0 {
    border-left: none;
}

.border-1 {
    border-width: 1px;
}

.border-t-1 {
    border-top-width: 1px;
}

.border-r-1 {
    border-right-width: 1px;
}

.border-b-1 {
    border-bottom-width: 1px;
}

.border-l-1 {
    border-left-width: 1px;
}

.border-solid {
    border-style: solid;
}

.border-t-solid {
    border-top-style: solid;
}

.border-r-solid {
    border-right-style: solid;
}

.border-b-solid {
    border-bottom-style: solid;
}

.border-l-solid {
    border-left-style: solid;
}

.border-r {
    border-radius: 100%;
}

.border-r-0 {
    border-radius: 0;
}

.border-r-4 {
    border-radius: 4px;
}

.border-r-8 {
    border-radius: 8px;
}

/* Overflow */
.overflow-hidden {
    overflow: hidden;
}

/* Width and Height */
.w-50 {
    width: 50%;
}

.w-100 {
    width: 100%;
}

.mw-100 {
    max-width: 100%;
}

.h-100 {
    height: 100%;
}

/* Visibility */
.visible {
    visibility: visible;
}

.invisible {
    visibility: hidden !important;
}

/* Others */
.no-resize {
    resize: none;
}

.no-click {
    pointer-events: none;
}

.responsive-image {
    max-width: 100%;
    height: auto;
}
