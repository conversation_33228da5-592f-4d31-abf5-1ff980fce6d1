@use "./rem-calc";

%icon-base {
    display: inline-block;
    width: var(--icon-base-width);
    height: var(--icon-base-height);
    cursor: pointer;
    background-color: var(--icon-primary-color);
    mask: {
        position: center;
        repeat: no-repeat;
        size: contain;
    }
}

%icon-base-background {
    display: inline-block;
    width: var(--icon-base-width);
    height: var(--icon-base-height);
    background: {
        position: center;
        repeat: no-repeat;
        size: contain;
    }
}

.gs-icon-organizations {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/admin-menu/organizations.svg");
    background-color: #afafaf;
}

.gs-icon-control-panel {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/admin-menu/control-panel.svg");
    background-color: #afafaf;
}

.gs-icon-file-sync {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/admin/file-sync.svg");
    background-color: #afafaf;
}

.gs-icon-field {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/field.svg");
    background-color: #afafaf;
}

.gs-icon-field-filled {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/field-filled.svg");
    background-color: #afafaf;
}

.gs-icon-ppp-products {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/ppp-products.svg");
    background-color: #afafaf;
}

.gs-icon-fertilizer-products {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/fertilizer-products.svg");
    background-color: #afafaf;
}

.gs-icon-area-sum {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/admin/area-sum.svg");
    background-color: #afafaf;
}

.gs-icon-linked-accounts {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/admin/linked-accounts.svg");
    background-color: #afafaf;
}

.gs-icon-units {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/admin/units.svg");
}

.gs-icon-info-circle {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/admin/info.svg");
}

.gs-icon-edit {
    @extend %icon-base;
    mask: {
        image: url("/assets/images/icons/edit.svg");
    }
}

.gs-icon-map {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/map.svg");
}

.gs-icon-menu {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/menu.svg");
}

.gs-icon-details {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/details.svg");
}

.gs-icon-pivot-position {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/pivot-position.svg");
}

.gs-icon-irrigated-area {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/irrigated-area.svg");
}

.gs-icon-worked-area {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/worked-area.svg");
}

.gs-icon-track {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/track.svg");
}

.gs-icon-weather-station {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/station/weather-station.svg");
}

.gs-icon-forecast {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/station/weather-forecast.svg");
}

.gs-icon-precipitation {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/station/precipitation.svg");
}

.gs-icon-station-radius {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/radius.svg");
}

.gs-icon-vegetation {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/vegetation.svg");
}

.gs-icon-water {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/water.svg");
}

.gs-icon-cloud {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/cloud.svg");
}

.gs-icon-soil {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/soil.svg");
}

.gs-icon-vra-map {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/vra-map.svg");
}

.gs-icon-current {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/current.svg");
}

.gs-icon-irrigation-tasks {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/irrigation-task.svg");
}

.gs-icon-delete {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/delete.svg");
}

.gs-icon-start-end-point {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/guidance-lines/start-end-point.svg");
}

.gs-icon-ab-line {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/guidance-lines/ab-line.svg");
}

.gs-icon-headland {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/guidance-lines/headland.svg");
}

.gs-icon-guidance-lines {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/guidance-lines/guidance-lines.svg");
}

.gs-icon-ab-line-projection {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/guidance-lines/ab-line-projection.svg");
}

.gs-icon-upload {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/upload.svg");
}

.gs-icon-download {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/download.svg");
}

.gs-icon-farm-data {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/farm-data.svg");
}

.gs-icon-filter {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/filter.svg");
}

.gs-icon-select {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/select.svg");
}

.gs-icon-file-excel {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/file-excel.svg");
}

.gs-icon-controls {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/controls.svg");
}

.gs-icon-copy {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/copy.svg");
}

.gs-icon-edit-layers {
    @extend %icon-base;
    mask: {
        image: url("/assets/images/icons/edit.svg");
    }
}

.gs-icon-geometry-intersect {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/geometry-intersect.svg");
}

.gs-icon-geometry-draw-object {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/geometry-draw-object.svg");
}

.gs-icon-geometry-split {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/geometry-split.svg");
}

.gs-icon-geometry-add-hole {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/geometry-add-hole.svg");
}

.gs-icon-geometry-remove-hole {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/geometry-remove-hole.svg");
}

.gs-icon-geometry-merge {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/geometry-merge.svg");
}

.gs-icon-slope {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/slope.svg");
}

.gs-icon-plus {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/plus.svg");
}

.gs-icon-zoom-to-layer {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/zoom-to-layer.svg");
}

.gs-icon-work-operation {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/work-operation.svg");
}

.gs-icon-implement {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/implement.svg");
}

.gs-icon-tractor {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tractor.svg");
}

.gs-icon-harvester {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/harvester.svg");
}

.gs-icon-sprayer {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/sprayer.svg");
}

.gs-icon-car {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/car.svg");
}

.gs-icon-truck {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/truck.svg");
}

.gs-icon-tractor {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tractor.svg");
}

.gs-icon-user {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/user.svg");
}

.gs-icon-tasks {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tasks.svg");
}

.gs-icon-task-state-planned,
.gs-icon-task-state-Planned {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tasks-states/planned.svg");
}

.gs-icon-task-state-scheduled,
.gs-icon-task-state-Scheduled {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tasks-states/scheduled.svg");
}

.gs-icon-task-state-Ongoing,
.gs-icon-task-state-OnGoing {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tasks-states/ongoing.svg");
}

.gs-icon-task-state-done-proposed,
.gs-icon-task-state-DoneProposed {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tasks-states/done-proposed.svg");
}

.gs-icon-task-state-done-approved,
.gs-icon-task-state-DoneApproved {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tasks-states/done-approved.svg");
}

.gs-icon-task-state-canceled,
.gs-icon-task-state-Canceled {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tasks-states/canceled.svg");
}

.gs-icon-calendar {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/calendar.svg");
}

.gs-icon-map-zoom-in {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/map/map-tools/zoom-in.svg");
}

.gs-icon-map-zoom-out {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/map/map-tools/zoom-out.svg");
}

.gs-icon-add-pin {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/map/map-tools/add-pin.svg");
}

.gs-icon-pin {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/map/pin.svg");
}

.gs-icon-location {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/location.svg");
}

.gs-icon-clock {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/clock.svg");
}

.gs-icon-add {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/add.svg");
}

.gs-icon-email {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/email.svg");
}

.gs-icon-settings {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/settings.svg");
}

.gs-icon-bucket {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/bucket.svg");
}

.gs-icon-bulb {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/bulb.svg");
}

.gs-icon-chart {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/chart.svg");
}

.gs-icon-eye {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/eye.svg");
}

.gs-icon-eye-slash {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/eye-slash.svg");
}

.gs-icon-group {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/group.svg");
}

// default arrow direction - right
.gs-icon-arrow {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/arrow.svg");
}

.gs-icon-arrow-right-circle {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/arrow-right-circle.svg");
}

.gs-icon-arrow-back {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/arrow-back.svg");
}

.gs-icon-arrow-circle-down {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/arrow-circle-down.svg");
}

.gs-icon-no-field {
    display: inline-block;
    width: rem-calc.rem-calc(32);
    height: rem-calc.rem-calc(32);
    background: url("/assets/images/icons/no-field.svg");
    background-size: contain;
}

.gs-icon-cross {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/cross.svg");
}

.gs-icon-cross-circle {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/cross-circle.svg");
}

.gs-icon-tick {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tick.svg");
}

.gs-icon-tick-circle {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/tick-circle.svg");
}

.gs-icon-border {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/border.svg");
}

.gs-icon-fill {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/fill.svg");
}

.gs-icon-warning {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/warning.svg");
}

.gs-icon-ongoing {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/ongoing.svg");
}

.gs-icon-typography {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/typography.svg");
}

.gs-icon-combine-geometries {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/combine-geometries.svg");
}

.gs-icon-administrative-map {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/administrative-map.svg");
}

.gs-icon-manage-kvs {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/manage-kvs.svg");
}

.gs-icon-kvs-item {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/kvs-item.svg");
}

.gs-icon-open-link-new-tab {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/open-link-new-tab.svg");
}

.gs-icon-reports {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/reports.svg");
}

.gs-icon-save {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/save.svg");
}

.gs-icon-move-to-top {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/move-to-top.svg");
}

.gs-icon-single-color {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/single-color.svg");
}

.gs-icon-by-attribute-color {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/multiple-color.svg");
}

.gs-icon-question {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/question.svg");
}

.gs-icon-warning {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/warning.svg");
}

.gs-icon-danger {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/danger.svg");
}
.gs-icon-contacts {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/contacts.svg");
}

.gs-icon-print {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/printer.svg");
}

.gs-icon-close-lock {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/password.svg");
}

.gs-icon-open-lock {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/lock-unlock.svg");
}

.gs-icon-contract {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/contract.svg");
}

.gs-icon-stamp {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/stamp.svg");
}

.gs-icon-rent {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/rent.svg");
}

.gs-icon-check {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/icon-check.svg");
}

.gs-icon-document {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/icon-contract.svg");
}

.gs-icon-inheritants {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/icon-inheritants.svg");
}

.gs-icon-edit-document {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/edit-document.svg");
}

.gs-icon-arrow-down {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/arrow-down.svg");
}

.gs-icon-owner {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/owner.svg");
}

.gs-icon-dead-owner {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/died-owner.svg");
}

.gs-icon-search {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/search.svg");
}
.gs-icon-annex {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/paper-clip.svg");
}

.gs-icon-data-preview-list {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/data-preview-list.svg");
}

.gs-icon-data-preview-table {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/data-preview-table.svg");
}

.gs-icon-owners-tree {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/owners-tree.svg");
}

.gs-icon-money {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/money.svg");
}

.gs-icon-document-text {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/document-text.svg");
}

.gs-icon-contract-filled {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/documents/contract-filled-no-color.svg");
}
.gs-icon-annex-filled {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/documents/annex-filled-no-color.svg");
}
.gs-icon-subleased-filled {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/documents/subleased-filled-no-color.svg");
}
.gs-icon-contract-filled-from-sublease {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/documents/contract-filled-no-color.svg");
}
.gs-icon-renew {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/documents/renew.svg");
}
.gs-icon-home {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/home.svg");
}

.gs-icon-multiedit {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/multi-edit.svg");
}

// Icons with background image

.gs-icon-sensor-air-humidity,
.gs-icon-sensor-humidity {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/humidity.svg");
}

.gs-icon-sensor-air-pressure {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/air-pressure.svg");
}

.gs-icon-sensor-air-temperature,
.gs-icon-sensor-air-temperature-0,
.gs-icon-sensor-air-temperature-minus6 {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/air-temperature.svg");
}

.gs-icon-sensor-battery {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/battery.svg");
}

.gs-icon-sensor-field-capacity {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/field-capacity.svg");
}

.gs-icon-sensor-leaf-wetness {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/leaf-wetness.svg");
}

.gs-icon-sensor-precipitation {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/precipitation.svg");
}

.gs-icon-sensor-soil-moisture {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/soil-moisture.svg");
}

.gs-icon-sensor-soil-temperature,
.gs-icon-sensor-soil-temperature-minus6,
.gs-icon-sensor-soil-temperature-minus12 {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/soil-temperature.svg");
}

.gs-icon-sensor-solar-radiation {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/solar-radiation.svg");
}

.gs-icon-sensor-wind-speed,
.gs-icon-sensor-wind-speed-max {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/station/wind-speed.svg");
}

.gs-icon-alert-info {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/alert-info.svg");
}

.gs-icon-alert-warning {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/alert-warning.svg");
}

.gs-icon-alert-success {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/alert-success.svg");
}

.gs-icon-alert-error {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/alert-error.svg");
}

.gs-icon-layer-colored-by-attribute {
    @extend %icon-base-background;
    background-image: url("/assets/images/icons/layer-colored-by-attribute.svg");
}

.gs-icon-bee {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/bee.svg");
}

.gs-icon-bee-filled {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/bee-filled.svg");
}

.gs-icon-bee-off {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/bee-off.svg");
}

.gs-icon-log-out {
    @extend %icon-base;
    mask-image: url("/assets/images/icons/logout.svg");
}
