@import "./colors.css";

:root {
    --body-bg-color: var(--neutral-color-1);

    /* Text */
    --text-primary-color: var(--neutral-color-8);
    --text-secondary-color: var(--neutral-color-7);
    --text-heading-color: var(--neutral-color-8);
    --text-subheading-color: var(--neutral-color-8);
    --text-link-color: var(--brand-color-primary-6);
    --text-white-color: var(--neutral-color-1);
    --text-black-color: var(--neutral-color-9);
    --text-blue-color: var(--secondary-color-dark-blue);
    --text-disabled-color: var(--neutral-color-6);
    --text-warning-color: var(--secondary-color-orange);
    --text-error-color: var(--brand-color-secondary-6);
    --text-highlighted-brand-color-primary: var(--brand-color-primary-6);
    --text-highlighted-brand-color-secondary: var(--brand-color-secondary-6);
    --text-highlighted-dark-color: var(--neutral-color-8);
    --text-light-grey-color: var(--neutral-color-6);
    --text-primary-color-green: var(--brand-color-primary-6);

    /* Border */
    --border-primary-color: #d9d9d9;
    --border-brand-color-primary: var(--brand-color-primary-6);
    --border-brand-color-primary-light: var(--brand-color-primary-3);
    --border-brand-color-secondary: var(--brand-color-secondary-6);
    --border-white-color: var(--neutral-color-1);
    --border-orange-color: var(--secondary-color-orange);
    --border-yellow-color: var(--secondary-color-yellow);
    --border-graphite-black-color: var(--neutral-color-8);
    --border-light-grey: var(--neutral-color-5);

    /* Backgrounds */
    --bg-primary-color: var(--neutral-color-1);
    --bg-light-color: var(--neutral-color-2);
    --bg-brand-color-primary: var(--brand-color-primary-6);
    --bg-brand-color-primary-light: var(--brand-color-primary-1);
    --bg-brand-color-secondary: var(--brand-color-secondary-6);
    --bg-orange-color: var(--secondary-color-orange);
    --bg-extra-light-blue-color: var(--secondary-color-extra-light-blue);
    --bg-graphite-black-color: var(--neutral-color-8);
    --bg-black-color: var(--neutral-color-9);
    --bg-yellow-color: var(--secondary-color-yellow);

    /* Box shadow */
    --box-shadow-light-color: rgba(0, 0, 0, 0.02);
    --box-shadow-dark-color: rgba(0, 0, 0, 0.15);

    /* States */
    --item-hover-bg-color: var(--neutral-color-3);
    --item-disabled-bg-color: var(--neutral-color-4);

    /* Statuses */
    --status-success: var(--brand-color-primary-6);
    --status-error: var(--brand-color-secondary-6);
    --status-warning: var(--secondary-color-yellow);
    --status-info: var(--secondary-color-dark-blue);

    /* Icons */
    --icon-primary-color: var(--neutral-color-7);
    --icon-disabled-color: var(--neutral-color-5);
    --icon-active-color: var(--brand-color-primary-6);
    --icon-hover-color: var(--brand-color-primary-6);
    --icon-brand-color-primary: var(--brand-color-primary-6);
    --icon-brand-color-primary-light: var(--brand-color-primary-3);
    --icon-brand-color-secondary: var(--brand-color-secondary-6);
    --icon-light-blue-color: var(--secondary-color-light-blue);
    --icon-dark-blue-color: var(--secondary-color-dark-blue);
    --icon-white-color: var(--neutral-color-1);
    --icon-black-color: var(--neutral-color-8);
    --icon-close-color: var(--neutral-color-6);
    --icon-warning-color: var(--secondary-color-orange);
    --icon-danger-color: var(--secondary-color-red);
    --icon-purple-color: var(--secondary-color-purple);

    /* Main navigation */
    --main-nav-bg-color: var(--neutral-color-1);
    --main-nav-item-icon-color: var(--neutral-color-6);
    --main-nav-item-text-color: var(--neutral-color-8);
    --main-nav-item-selected-icon-color: var(--brand-color-primary-6);
    --main-nav-item-selected-bg-color: var(--neutral-color-3);
    --main-nav-item-selected-text-color: var(--neutral-color-8);
    --main-nav-border-color: var(--neutral-color-3);

    /* Primary brand color buttons */
    /* --primary */
    --btn-primary-text-color-brand-primary: var(--neutral-color-1);
    --btn-primary-bg-color-brand-primary: var(--brand-color-primary-6);
    --btn-primary-bg-color-brand-light: var(--brand-color-primary-1);
    --btn-primary-border-color-brand-primary: var(--brand-color-primary-6);

    --btn-primary-hover-text-color-brand-primary: var(--neutral-color-1);
    --btn-primary-hover-bg-color-brand-primary: var(--brand-color-primary-6);
    --btn-primary-hover-border-color-brand-primary: var(
        --brand-color-primary-6
    );

    --btn-primary-active-text-color-brand-primary: var(--neutral-color-1);
    --btn-primary-active-bg-color-brand-primary: var(--brand-color-primary-7);
    --btn-primary-active-border-color-brand-primary: var(
        --brand-color-primary-7
    );

    /* --default */
    --btn-default-text-color-brand-primary: var(--neutral-color-8);
    --btn-default-bg-color-brand-primary: var(--neutral-color-1);
    --btn-default-border-color-brand-primary: var(--border-primary-color);

    --btn-default-hover-text-color-brand-primary: var(--brand-color-primary-6);
    --btn-default-hover-bg-color-brand-primary: var(--neutral-color-1);
    --btn-default-hover-border-color-brand-primary: var(
        --brand-color-primary-2
    );

    --btn-default-active-text-color-brand-primary: var(--brand-color-primary-7);
    --btn-default-active-bg-color-brand-primary: var(--neutral-color-1);
    --btn-default-active-border-color-brand-primary: var(
        --brand-color-primary-7
    );

    /* --link */
    --btn-link-text-color-brand-primary: var(--brand-color-primary-6);
    --btn-link-hover-text-color-brand-primary: var(--brand-color-primary-6);
    --btn-link-active-text-color-brand-primary: var(--brand-color-primary-7);

    /* --text */
    --btn-text-text-color-brand-primary: var(--neutral-color-8);
    --btn-text-hover-text-color-brand-primary: var(--brand-color-primary-6);
    --btn-text-hover-bg-color-brand-primary: var(--neutral-color-1);
    --btn-text-active-text-color-brand-primary: var(--brand-color-primary-7);
    --btn-text-active-bg-color-brand-primary: var(--neutral-color-1);

    /* Secondary brand color buttons */
    /* --primary */
    --btn-primary-text-color-brand-secondary: var(--neutral-color-1);
    --btn-primary-bg-color-brand-secondary: var(--brand-color-secondary-6);
    --btn-primary-border-color-brand-secondary: var(--brand-color-secondary-6);

    --btn-primary-hover-text-color-brand-secondary: var(--neutral-color-1);
    --btn-primary-hover-bg-color-brand-secondary: var(
        --brand-color-secondary-6
    );
    --btn-primary-hover-border-color-brand-secondary: var(
        --brand-color-secondary-6
    );

    --btn-primary-active-text-color-brand-secondary: var(--neutral-color-1);
    --btn-primary-active-bg-color-brand-secondary: var(
        --brand-color-secondary-7
    );
    --btn-primary-active-border-color-brand-secondary: var(
        --brand-color-secondary-7
    );

    /* --default */
    --btn-default-text-color-brand-secondary: var(--brand-color-secondary-6);
    --btn-default-bg-color-brand-secondary: var(--neutral-color-1);
    --btn-default-border-color-brand-secondary: var(--brand-color-secondary-6);

    --btn-default-hover-text-color-brand-secondary: var(
        --brand-color-secondary-6
    );
    --btn-default-hover-bg-color-brand-secondary: var(--neutral-color-1);
    --btn-default-hover-border-color-brand-secondary: var(
        --brand-color-secondary-6
    );

    --btn-default-active-text-color-brand-secondary: var(
        --brand-color-secondary-7
    );
    --btn-default-active-bg-color-brand-secondary: var(--neutral-color-1);
    --btn-default-active-border-color-brand-secondary: var(
        --brand-color-secondary-7
    );

    /* --link */
    --btn-link-text-color-brand-secondary: var(--brand-color-secondary-6);
    --btn-link-hover-text-color-brand-secondary: var(--brand-color-secondary-6);
    --btn-link-active-text-color-brand-secondary: var(
        --brand-color-secondary-7
    );

    /* --text */
    --btn-text-text-color-brand-secondary: var(--neutral-color-8);
    --btn-text-hover-text-color-brand-secondary: var(--brand-color-secondary-4);
    --btn-text-hover-bg-color-brand-secondary: var(--neutral-color-3);
    --btn-text-hover-border-color-brand-secondary: var(--neutral-color-3);
    --btn-text-active-text-color-brand-secondary: var(
        --brand-color-secondary-7
    );

    /* Disabled buttons */
    --btn-disabled-text-color: var(--neutral-color-6);
    --btn-disabled-bg-color: var(--neutral-color-3);
    --btn-disabled-border-color: var(--neutral-color-6);

    /* Custom buttons */
    --btn-custom-text-color: var(--neutral-color-8);
    --btn-custom-icon-color: var(--neutral-color-8);
    --btn-custom-bg-color: var(--neutral-color-3);
    --btn-custom-border-color: var(--neutral-color-3);

    --btn-custom-active-text-color: var(--brand-color-primary-6);
    --btn-custom-active-icon-color: var(--brand-color-primary-6);
    --btn-custom-active-bg-color: var(--secondary-color-extra-light-blue);
    --btn-custom-active-border-color: var(--secondary-color-extra-light-blue);

    /* --dark */
    --btn-custom-dark-text-color: var(--neutral-color-1);
    --btn-custom-dark-bg-color: var(--neutral-color-8);
    --btn-custom-dark-border-color: var(--neutral-color-8);

    --btn-custom-dark-hover-bg-color: var(--brand-color-secondary-6);
    --btn-custom-dark-hover-border-color: var(--brand-color-secondary-6);

    --btn-custom-dark-active-bg-color: var(--brand-color-secondary-6);
    --btn-custom-dark-active-border-color: var(--brand-color-secondary-6);

    /* Group buttons */
    --btn-group-text-color: var(--neutral-color-8);
    --btn-group-bg-color: var(--neutral-color-2);
    --btn-group-border-color: var(--neutral-color-2);
    --btn-group-selected-text-color: var(--brand-color-primary-6);
    --btn-group-selected-bg-color: var(--secondary-color-extra-light-blue);
    --btn-group-disabled-text-color: var(--neutral-color-6);
    --btn-group-disabled-bg-color: var(--neutral-color-2);

    /* Segmented */
    --segmented-bg-color: var(--neutral-color-2);
    --segmented-selected-bg-color: var(--secondary-color-extra-light-blue);
    --segmented-text-color: var(--neutral-color-8);
    --segmented-selected-text-color: var(--brand-color-primary-6);

    /* Tabs */
    --tabs-text-color: var(--neutral-color-8);
    --tabs-icon-color: var(--neutral-color-7);
    --tabs-bg-color: var(--neutral-color-1);
    --tabs-border-color: var(--border-primary-color);

    --tabs-hover-text-color: var(--brand-color-primary-6);
    --tabs-hover-icon-color: var(--neutral-color-6);
    --tabs-hover-bg-color: var(--neutral-color-1);

    --tabs-active-text-color: var(--brand-color-primary-6);
    --tabs-active-icon-color: var(--brand-color-secondary-6);
    --tabs-active-bg-color: var(--neutral-color-1);
    --tabs-active-border-color: var(--brand-color-primary-6);

    /* Tags*/
    --info-tag-text-color: var(--text-color-info);

    /* Timeline */
    --timeline-bg-fields: #edeff5;
    --timeline-bg-soils: var(--neutral-color-1);
    --timeline-text-color: var(--neutral-color-6);
    --timeline-text-active-color: var(--neutral-color-8);
    --timeline-text-hover-color: var(--neutral-color-8);
    --timeline-dot-color: var(--neutral-color-6);
    --timeline-dot-active-color: var(--brand-color-primary-6);
    --timeline-dot-disabled-color: var(--neutral-color-4);
    --timeline-navigation-color: #292d32;
    --timeline-skeleton-color: var(--neutral-color-6);

    /* Radio group buttons */
    --radio-button-text-color: var(--neutral-color-8);
    --radio-button-bg-color: var(--neutral-color-2);
    --radio-button-border-color: var(--neutral-color-2);
    --radio-button-checked-text-color: var(--brand-color-primary-6);
    --radio-button-checked-bg-color: var(--secondary-color-extra-light-blue);
    --radio-button-disabled-text-color: var(--neutral-color-6);
    --radio-button-disabled-bg-color: var(--neutral-color-2);

    /* Radio */
    --radio-label-color: var(--neutral-color-8);
    --radio-border-color: var(--neutral-color-6);
    --radio-checked-border-color: var(--brand-color-primary-6);
    --radio-dot-color: var(--brand-color-primary-6);
    --radio-disabled-label-color: var(--neutral-color-6);
    --radio-disabled-border-color: var(--neutral-color-6);
    --radio-disabled-bg-color: var(--neutral-color-3);
    --radio-disabled-dot-color: var(--neutral-color-6);

    /* Checkbox */
    --checkbox-label-color: var(--neutral-color-8);
    --checkbox-border-color: var(--neutral-color-6);
    --checkbox-bg-color: var(--neutral-color-1);
    --checkbox-check-color: var(--neutral-color-1);
    --checkbox-checked-border-color: var(--brand-color-primary-6);
    --checkbox-checked-bg-color: var(--brand-color-primary-6);
    --checkbox-disabled-label-color: var(--neutral-color-6);
    --checkbox-disabled-border-color: var(--neutral-color-6);
    --checkbox-disabled-bg-color: var(--neutral-color-3);
    --checkbox-disabled-check-color: var(--neutral-color-6);
    --checkbox-hover-border-color: var(--brand-color-primary-6);
    --checkbox-intermediate-check-color: var(--secondary-color-light-blue);

    /* Form */
    --form-label-text-color: var(--neutral-color-8);
    --form-label-required-color: var(--brand-color-secondary-6);
    --form-item-text-color: var(--neutral-color-8);
    --form-item-placeholder-color: var(--neutral-color-6);
    --form-item-icon-color: var(--neutral-color-6);
    --form-item-border-color: var(--border-primary-color);
    --form-item-bg-color: var(--neutral-color-1);
    --form-item-hover-border-color: var(--brand-color-primary-3);
    --form-item-hover-icon-color: var(--neutral-color-6);
    --form-item-focused-border-color: var(--brand-color-primary-3);
    --form-item-focused-box-shadow-color: var(--brand-color-primary-1);
    --form-item-error-border-color: var(--brand-color-secondary-6);
    --form-item-error-text-color: var(--brand-color-secondary-6);
    --form-item-disabled-text-color: var(--neutral-color-6);
    --form-item-disabled-border-color: var(--border-primary-color);
    --form-item-disabled-bg-color: var(--neutral-color-2);

    --select-arrow-color: var(--neutral-color-6);
    --select-close-color: var(--neutral-color-6);
    --select-close-hover-color: var(--brand-color-secondary-6);
    --select-item-selected-text-color: var(--brand-color-primary-6);
    --select-item-selected-bg-color: var(--secondary-color-extra-light-blue);
    --select-single-selected-item: var(--neutral-color-8);
    --select-multiple-item-text-color: var(--neutral-color-8);
    --select-multiple-item-bg-color: var(--neutral-color-3);
    --select-multiple-item-border-color: var(--neutral-color-3);
    --select-multiple-item-close-icon-color: var(--secondary-color-dark-blue);

    /* Date picker */
    --date-picker-border-color: var(--border-primary-color);
    --date-picker-cell-hover-bg-color: var(--neutral-color-0);
    --date-picker-cell-hover-border-color: var(--brand-color-primary-6);
    --date-picker-range-bg-color: var(--neutral-color-3);
    --date-picker-range-hover-bg-color: var(--neutral-color-6);
    --date-picker-range-text-color: var(--neutral-color-8);
    --date-picker-range-bg-color: var(--neutral-color-3);
    --date-picker-range-hover-border-color: var(--brand-color-primary-6);
    --date-picker-range-border-color: var(--neutral-color-3);

    /* Switch */
    --switch-text-color: var(--neutral-color-1);
    --switch-icon-color: var(--neutral-color-1);
    --switch-handle-bg-color: var(--neutral-color-1);
    --switch-bg-color: var(--neutral-color-6);
    --switch-loading-indicator-color: var(--neutral-color-6);
    --switch-loading-handle-bg-color: var(--neutral-color-3);
    --switch-loading-bg-color: var(--neutral-color-4);
    --switch-checked-bg-color: var(--brand-color-primary-6);
    --switch-checked-loading-indicator-color: var(--brand-color-primary-2);
    --switch-checked-loading-handle-bg-color: var(--neutral-color-2);
    --switch-checked-loading-bg-color: var(--brand-color-primary-2);
    --switch-disabled-bg-color: var(--neutral-color-4);
    --switch-disabled-checked-bg-color: var(--brand-color-primary-1);

    /* Table */
    --table-header-text-color: var(--secondary-color-dark-blue);
    --table-header-bg-color: var(--neutral-color-2);
    --table-border-color: var(--border-primary-color);
    --table-column-sorter-icon-bg-color: var(--neutral-color-6);
    --table-row-hover-bg-color: var(--neutral-color-2);
    --table-row-bg-error: #fff1f0;
    --table-row-bg-warning: #fff7e6;
    --table-text-highlighted-color: var(--secondary-color-dark-blue);
    --table-text-color-purple: var(--secondary-color-purple);
    --table-selected-row-bg-color: var(--brand-color-primary-1);
    --table-highlighted-row-bg-color: var(--secondary-color-extra-light-blue);
    --table-expanded-row-text-color: var(--brand-color-primary-6);
    --table-expanded-row-bg-color: var(--neutral-color-2);
    --table-footer-bg-color: var(--secondary-color-extra-light-blue);

    /* Map toolbar */
    --map-toolbar-bg: var(--neutral-color-8);
    --map-toolbar-icon-color: var(--neutral-color-1);
    --map-toolbar-border-color: var(--border-primary-color);
    --map-toolbar-icon-hover-color: var(--brand-color-secondary-4);
    --map-toolbar-icon-active-color: var(--brand-color-secondary-4);
    --map-toolbar-icon-disabled-color: var(--neutral-color-3);
    --map-tool-custom-disabled-bg-color: var(--neutral-color-2);
    --map-layers-menu-bg-color: var(--neutral-color-8);

    /* Fields - Index slider */
    --index-slider-bg-color: rgba(255, 255, 255, 0.2);
    --index-slider-header-bg-color: var(--neutral-color-8);
    --index-slider-icon-color: var(--secondary-color-dark-blue);
    --index-slider-icon-bg-color: var(--neutral-color-6);
    --index-slider-icon-border-color: var(--border-primary-color);
    --index-slider-icon-selected-color: var(--neutral-color-1);
    --index-slider-icon-selected-bg-color: var(--brand-color-primary-6);
    --index-slider-scrollbar-thumb-color: var(--neutral-color-6);
    --index-slider-item-bg-color: rgba(0, 0, 0, 0.75);
    --index-slider-item-selected-bg-color: rgba(0, 0, 0, 0.75);
    --index-slider-item-selected-border-color: var(--brand-color-primary-3);
    --index-slider-icon-cloud-color: var(--brand-color-primary-3);
    --index-slider-date-text-color: var(--neutral-color-1);
    --index-slider-info-text-color: var(--brand-color-primary-3);
    --index-slider-footer-text-color: var(--neutral-color-1);
    --index-slider-footer-icon-color: var(--neutral-color-1);

    /* Platforms custom colors */
    --platforms-avg-water-rate-text-color: var(--brand-color-primary-6);
    --platforms-avg-water-rate-icon-color: var(--brand-color-primary-6);
    --platforms-avg-water-rate-bg-color: var(--secondary-color-light-blue);

    --platforms-irrigated-area-text-color: var(--brand-color-secondary-6);
    --platforms-irrigated-area-icon-color: var(--brand-color-secondary-6);
    --platforms-irrigated-area-bg-color: var(--brand-color-secondary-1);

    --platform-status-irrigation: var(--brand-color-primary-4);
    --platform-status-off: var(--secondary-color-purple);
    --platform-status-active: var(--secondary-color-orange);
    --platform-status-inactive: var(--neutral-color-7);
    --platform-status-movement: var(--secondary-color-orange);
    --platform-status-alarm: var(--brand-color-secondary-7);
    --platform-status-transportation: var(--secondary-color-purple);

    /* Machines custom colors */
    --machines-area-text-color: var(--brand-color-primary-6);
    --machines-area-icon-color: var(--brand-color-primary-6);
    --machines-area-bg-color: var(--secondary-color-light-blue);

    --machines-distance-text-color: var(--neutral-color-1);
    --machines-distance-icon-color: var(--neutral-color-1);
    --machines-distance-bg-color: var(--secondary-color-light-purple);

    --machines-fuel-text-color: var(--brand-color-secondary-6);
    --machines-fuel-icon-color: var(--brand-color-secondary-6);
    --machines-fuel-bg-color: var(--brand-color-secondary-1);

    --machine-status-moving: var(--brand-color-primary-4);
    --machine-status-off: var(--brand-color-secondary-6);
    --machine-status-idle: var(--secondary-color-orange);

    /* Guidance lines */
    --guidance-line-info-bg-color: #edeff5;
    --guidance-line-info-text-color: #afb3b8;
    --guidance-line-info-icon-color: #afb3b8;
    --guidance-line-info-active-bg: var(--alert-info-bg);
    --guidance-line-info-active-text-color: var(--alert-info-text-color);
    --guidance-line-info-active-icon-color: var(--brand-color-primary-2);

    /* Slider */
    --slider-track-bg-color: var(--brand-color-primary-4);
    --slider-rail-bg-color: var(--neutral-color-3);

    /* Kvs store*/
    --kvs-store-loaded-kvs-count-bg-color: var(--brand-color-secondary-1);
    --kvs-store-available-kvs-count-bg-color: var(--secondary-color-light-blue);

    /* Admin table */
    --admin-table-footer-color: #f0f2f5;
    --admin-table-selected-element-color: #f0f2f5;
    --admin-table-selected-row-bg-color: #fafafa;
    --admin-table-border-color: #e8e8e8;

    /* Admin form */
    --admin-form-label-color: #595959;

    /* Admin select for sampling */
    --admin-select-for-sampling-plot-type-container: #f0f2f5;

    /* Admin icons */
    --admin-icon-gray: #f0f2f5;

    /* Admin tags */
    --admin-active-tag-text-color: #488f4a;
    --admin-active-border-color: #488f4a;
    --admin-active-tag-bg-color: #c4f3d4;

    --admin-inactive-tag-text-color: #891d20;
    --admin-inactive-tag-border-color: #891d20;
    --admin-inactive-tag-bg-color: #ff9c9c;

    --admin-default-tag-text-color: var(--primary-color-500);
    --admin-default-tag-border-color: var(--primary-color-500);
    --admin-default-tag-bg-color: #e9f1fc;

    /* Admin list*/
    --admin-list-header-bg-color: #fafafa;

    /* Breadcrumbs*/
    --breadcrumb-base-color: var(--neutral-color-7);
    --breadcrumb-separator-color: var(--neutral-color-7);
    --breadcrumb-link-color: var(--neutral-color-7);
}
